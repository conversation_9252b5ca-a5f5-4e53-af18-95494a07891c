{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/input.tsx"], "sourcesContent": ["import { cn } from \"@/library/utils\";\r\nimport * as React from \"react\";\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    );\r\n  }\r\n);\r\nInput.displayName = \"Input\";\r\n\r\nexport { Input };\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/library/utils\";\r\n\r\nfunction Skeleton({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLDivElement>) {\r\n  return (\r\n    <div\r\n      className={cn(\"animate-pulse rounded-md bg-primary/10\", className)}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Skeleton };\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGf;KAVS"}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@/library/utils\";\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nCard.displayName = \"Card\";\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardHeader.displayName = \"CardHeader\";\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardTitle.displayName = \"CardTitle\";\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardDescription.displayName = \"CardDescription\";\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n));\r\nCardContent.displayName = \"CardContent\";\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n));\r\nCardFooter.displayName = \"CardFooter\";\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardDescription,\r\n  CardContent,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,8JAAM,UAAU,MAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,8JAAM,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,8JAAM,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,8JAAM,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,8JAAM,UAAU,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/pools/StatsSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Skeleton } from \"@/components/ui/skeleton\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\nexport default function StatsSection() {\r\n  const [stats, setStats] = useState<null | {\r\n    total_active_candidates: number;\r\n    total_temp_candidates: number;\r\n    temp_candidates_no_resume: number;\r\n    temp_candidates_no_resume_with_email: number;\r\n    temp_candidates_no_resume_with_phone: number;\r\n    temp_candidates_no_resume_with_phone_email: number;\r\n    total_candidates: number;\r\n    temp_candidates_with_resume: number;\r\n  }>(null);\r\n\r\n  useEffect(() => {\r\n    const fetchStats = async () => {\r\n      try {\r\n        const res = await fetch(\"/api/pools/stats\");\r\n        const data = await res.json();\r\n        setStats(data.stats);\r\n      } catch (error) {\r\n        console.error(\"Error fetching stats:\", error);\r\n      }\r\n    };\r\n\r\n    fetchStats();\r\n  }, []);\r\n\r\n  const cardStyle = \"w-full pt-3 pb-0\";\r\n\r\n  const SkeletonCard = () => (\r\n    <Card className={cardStyle}>\r\n      <CardContent className=\"flex flex-col gap-2\">\r\n        <Skeleton className=\"h-4 w-2/3\" />\r\n        <Skeleton className=\"h-6 w-1/3\" />\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n\r\n  const readableLabels: Record<string, string> = {\r\n    total_candidates: \"Total Candidates\",\r\n    total_active_candidates: \"Total Active Candidates\",\r\n    total_temp_candidates: \"Total Temp Candidates\",\r\n    temp_candidates_with_resume: \"Temp Candidates With Resume\",\r\n    temp_candidates_no_resume_with_email: \"Temp w/o Resume + Email\",\r\n    temp_candidates_no_resume_with_phone: \"Temp w/o Resume + Phone\",\r\n    temp_candidates_no_resume_with_phone_email:\r\n      \"Temp w/o Resume + Phone + Email\",\r\n    total_tagged_candidates: \"Total Tagged Candidates\",\r\n  };\r\n\r\n  if (!stats) {\r\n    return (\r\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-6\">\r\n        {Array(8)\r\n          .fill(0)\r\n          .map((_, i) => (\r\n            <SkeletonCard key={i} />\r\n          ))}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-6\">\r\n      {Object.entries(stats).map(([key, value]) => (\r\n        <div\r\n          key={key}\r\n          className=\"p-4 bg-gray-100 rounded-md shadow-sm flex flex-col\"\r\n        >\r\n          <p className=\"text-gray-700 font-medium\">\r\n            {readableLabels[key] ?? key}\r\n          </p>\r\n          <p className=\"text-xl font-bold text-indigo-700\">\r\n            {value.toLocaleString()}\r\n          </p>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAS9B;IAEH,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;qDAAa;oBACjB,IAAI;wBACF,MAAM,MAAM,MAAM,MAAM;wBACxB,MAAM,OAAO,MAAM,IAAI,IAAI;wBAC3B,SAAS,KAAK,KAAK;oBACrB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,yBAAyB;oBACzC;gBACF;;YAEA;QACF;iCAAG,EAAE;IAEL,MAAM,YAAY;IAElB,MAAM,eAAe,kBACnB,6LAAC,4HAAA,CAAA,OAAI;YAAC,WAAW;sBACf,cAAA,6LAAC,4HAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6LAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,gIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;;;;;;IAK1B,MAAM,iBAAyC;QAC7C,kBAAkB;QAClB,yBAAyB;QACzB,uBAAuB;QACvB,6BAA6B;QAC7B,sCAAsC;QACtC,sCAAsC;QACtC,4CACE;QACF,yBAAyB;IAC3B;IAEA,IAAI,CAAC,OAAO;QACV,qBACE,6LAAC;YAAI,WAAU;sBACZ,MAAM,GACJ,IAAI,CAAC,GACL,GAAG,CAAC,CAAC,GAAG,kBACP,6LAAC,kBAAkB;;;;;;;;;;IAI7B;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,OAAO,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBACtC,6LAAC;gBAEC,WAAU;;kCAEV,6LAAC;wBAAE,WAAU;kCACV,cAAc,CAAC,IAAI,IAAI;;;;;;kCAE1B,6LAAC;wBAAE,WAAU;kCACV,MAAM,cAAc;;;;;;;eAPlB;;;;;;;;;;AAaf;GA9EwB;KAAA"}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\";\r\n\r\nimport { cn } from \"@/library/utils\";\r\n\r\nconst Tabs = TabsPrimitive.Root;\r\n\r\nconst TabsList = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.List>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.List\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex h-9 items-center justify-center rounded-lg bg-muted p-1 text-muted-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsList.displayName = TabsPrimitive.List.displayName;\r\n\r\nconst TabsTrigger = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Trigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-md px-3 py-1 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\r\n\r\nconst TabsContent = React.forwardRef<\r\n  React.ElementRef<typeof TabsPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\r\n>(({ className, ...props }, ref) => (\r\n  <TabsPrimitive.Content\r\n    ref={ref}\r\n    className={cn(\r\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTabsContent.displayName = TabsPrimitive.Content.displayName;\r\n\r\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,OAAO,oKAAc,IAAI;AAE/B,MAAM,yBAAW,8JAAM,UAAU,MAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,IAAI;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,oKAAc,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAc,OAAO,CAAC,WAAW;AAE3D,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,oKAAc,OAAO;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,oKAAc,OAAO,CAAC,WAAW"}}, {"offset": {"line": 369, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 375, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/Loading.tsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst Loading = ({ height }: { height?: string }) => {\r\n  return (\r\n    <div\r\n      className={`flex justify-center items-center ${\r\n        height ? height : \"h-[100vh]\"\r\n      } w-full`}\r\n    >\r\n      <svg\r\n        className=\"ml-2 mt-0.5 size-10 animate-spin\"\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n        fill=\"none\"\r\n        viewBox=\"0 0 24 24\"\r\n      >\r\n        <circle\r\n          className=\"opacity-25\"\r\n          cx=\"12\"\r\n          cy=\"12\"\r\n          r=\"10\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth=\"4\"\r\n        ></circle>\r\n        <path\r\n          className=\"opacity-75\"\r\n          fill=\"currentColor\"\r\n          d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\r\n        ></path>\r\n      </svg>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Loading;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,UAAU,CAAC,EAAE,MAAM,EAAuB;IAC9C,qBACE,6LAAC;QACC,WAAW,CAAC,iCAAiC,EAC3C,SAAS,SAAS,YACnB,OAAO,CAAC;kBAET,cAAA,6LAAC;YACC,WAAU;YACV,OAAM;YACN,MAAK;YACL,SAAQ;;8BAER,6LAAC;oBACC,WAAU;oBACV,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,QAAO;oBACP,aAAY;;;;;;8BAEd,6LAAC;oBACC,WAAU;oBACV,MAAK;oBACL,GAAE;;;;;;;;;;;;;;;;;AAKZ;KA7BM;uCA+BS"}}, {"offset": {"line": 429, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\";\r\n\r\nimport { cn } from \"@/library/utils\";\r\n\r\nconst Table = React.forwardRef<\r\n  HTMLTableElement,\r\n  React.HTMLAttributes<HTMLTableElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div className=\"relative w-full overflow-auto\">\r\n    <table\r\n      ref={ref}\r\n      className={cn(\"w-full caption-bottom text-sm\", className)}\r\n      {...props}\r\n    />\r\n  </div>\r\n));\r\nTable.displayName = \"Table\";\r\n\r\nconst TableHeader = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\r\n));\r\nTableHeader.displayName = \"TableHeader\";\r\n\r\nconst TableBody = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tbody\r\n    ref={ref}\r\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\r\n    {...props}\r\n  />\r\n));\r\nTableBody.displayName = \"TableBody\";\r\n\r\nconst TableFooter = React.forwardRef<\r\n  HTMLTableSectionElement,\r\n  React.HTMLAttributes<HTMLTableSectionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tfoot\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableFooter.displayName = \"TableFooter\";\r\n\r\nconst TableRow = React.forwardRef<\r\n  HTMLTableRowElement,\r\n  React.HTMLAttributes<HTMLTableRowElement>\r\n>(({ className, ...props }, ref) => (\r\n  <tr\r\n    ref={ref}\r\n    className={cn(\r\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableRow.displayName = \"TableRow\";\r\n\r\nconst TableHead = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.ThHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <th\r\n    ref={ref}\r\n    className={cn(\r\n      \"h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableHead.displayName = \"TableHead\";\r\n\r\nconst TableCell = React.forwardRef<\r\n  HTMLTableCellElement,\r\n  React.TdHTMLAttributes<HTMLTableCellElement>\r\n>(({ className, ...props }, ref) => (\r\n  <td\r\n    ref={ref}\r\n    className={cn(\r\n      \"p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n));\r\nTableCell.displayName = \"TableCell\";\r\n\r\nconst TableCaption = React.forwardRef<\r\n  HTMLTableCaptionElement,\r\n  React.HTMLAttributes<HTMLTableCaptionElement>\r\n>(({ className, ...props }, ref) => (\r\n  <caption\r\n    ref={ref}\r\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n));\r\nTableCaption.displayName = \"TableCaption\";\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,8JAAM,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,8JAAM,UAAU,OAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,8JAAM,UAAU,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0IACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,8JAAM,UAAU,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wFACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,8JAAM,UAAU,QAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,aAAa,WAAW,GAAG"}}, {"offset": {"line": 567, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 573, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/workforce-index/CategoriesTable.tsx"], "sourcesContent": ["import { SubCategoryPool } from \"@/app/workforce-index/page\";\r\n\r\nimport Loading from \"@/components/Loading\";\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport { ArrowUpDown } from \"lucide-react\";\r\n\r\nexport default function CategoriesTable({\r\n  data,\r\n  handleSort,\r\n  loading,\r\n}: {\r\n  data: SubCategoryPool[];\r\n  handleSort: (key: keyof SubCategoryPool) => void;\r\n  loading: boolean;\r\n}) {\r\n  // copy & paste your existing table logic here using `data` directly\r\n  return (\r\n    <div className=\"border rounded-lg overflow-hidden\">\r\n      {/* Fixed Header */}\r\n      <div className=\"bg-gray-300\">\r\n        <Table className=\"w-full\">\r\n          <TableHeader>\r\n            <TableRow className=\"bg-gray-900 hover:bg-gray-900 cursor-pointer\">\r\n              <TableHead\r\n                className=\"py-3 cursor-pointer w-[300px] text-white\"\r\n                onClick={() => handleSort(\"subcategory\")}\r\n              >\r\n                Subcategory <ArrowUpDown className=\"inline ml-1 h-4 w-4\" />\r\n              </TableHead>\r\n              <TableHead\r\n                className=\"py-3 cursor-pointer w-auto text-white sticky right-[100px]\"\r\n                onClick={() => handleSort(\"category\")}\r\n              >\r\n                Go To Market <ArrowUpDown className=\"inline ml-1 h-4 w-4\" />\r\n              </TableHead>\r\n              <TableHead\r\n                className=\"w-[90px] py-3 cursor-pointer sticky right-[100px] bg-gray-900 text-white\"\r\n                onClick={() => handleSort(\"high\")}\r\n              >\r\n                High Matches\r\n                <ArrowUpDown className=\"inline ml-1 h-4 w-4\" />\r\n              </TableHead>\r\n              <TableHead\r\n                className=\"w-[90px] py-3 cursor-pointer sticky right-[100px] text-white\"\r\n                onClick={() => handleSort(\"high_match_availibility_count\")}\r\n              >\r\n                High Match Availability Count\r\n                <ArrowUpDown className=\"inline ml-1 h-4 w-4\" />\r\n              </TableHead>\r\n              <TableHead\r\n                className=\"w-[90px] py-3 cursor-pointer sticky right-[100px] text-white\"\r\n                onClick={() => handleSort(\"high_match_address_count\")}\r\n              >\r\n                High Match Address Count\r\n                <ArrowUpDown className=\"inline ml-1 h-4 w-4\" />\r\n              </TableHead>\r\n              <TableHead\r\n                className=\"w-[90px] py-3 cursor-pointer sticky right-[100px] bg-gray-900 text-white\"\r\n                onClick={() => handleSort(\"medium\")}\r\n              >\r\n                Medium Matches\r\n                <ArrowUpDown className=\"inline ml-1 h-4 w-4\" />\r\n              </TableHead>\r\n              <TableHead\r\n                className=\"w-[90px] py-3 cursor-pointer sticky right-[100px] text-white\"\r\n                onClick={() => handleSort(\"medium_match_availibility_count\")}\r\n              >\r\n                Medium Match Availability Count\r\n                <ArrowUpDown className=\"inline ml-1 h-4 w-4\" />\r\n              </TableHead>\r\n              <TableHead\r\n                className=\"w-[90px] py-3 cursor-pointer sticky right-[0px] text-white\"\r\n                onClick={() => handleSort(\"medium_match_address_count\")}\r\n              >\r\n                Medium Match Address Count\r\n                <ArrowUpDown className=\"inline ml-1 h-4 w-4\" />\r\n              </TableHead>\r\n            </TableRow>\r\n          </TableHeader>\r\n        </Table>\r\n      </div>\r\n\r\n      {/* Scrollable Table Body */}\r\n      {loading ? (\r\n        <Loading height=\"h-[37vh]\" />\r\n      ) : (\r\n        <div className=\"max-h-[37vh] overflow-auto\">\r\n          <Table className=\"w-full\">\r\n            <TableBody>\r\n              {data.map((skill, ind) => (\r\n                <TableRow\r\n                  key={ind}\r\n                  className={`${ind % 2 === 1 ? \"bg-gray-50\" : \"\"}`}\r\n                >\r\n                  <TableCell className=\"pl-4 py-3 capitalize w-[300px]\">\r\n                    {skill.subcategory}\r\n                  </TableCell>\r\n                  <TableCell className=\"py-3 pl-0 sticky right-[100px] capitalize w-auto\">\r\n                    {skill.category ?? <p className=\"pl-6\">-</p>}\r\n                  </TableCell>\r\n                  <TableCell\r\n                    className={`w-[90px] py-3 sticky right-[0px] bg-white pl-4 ${\r\n                      ind % 2 === 1 ? \"bg-gray-50\" : \"\"\r\n                    }`}\r\n                  >\r\n                    {skill.high}\r\n                  </TableCell>\r\n                  <TableCell className=\"w-[90px] capitalize  py-3 sticky right-[100px] bg-white pl-4\">\r\n                    {skill.high_match_availibility_count ?? 0}\r\n                  </TableCell>\r\n                  <TableCell className=\"w-[90px] capitalize  py-3 sticky right-[100px] bg-white pl-4\">\r\n                    {skill.high_match_address_count ?? 0}\r\n                  </TableCell>\r\n                  <TableCell\r\n                    className={`w-[90px] py-3 sticky right-[100px] bg-white pl-4 ${\r\n                      ind % 2 === 1 ? \"bg-gray-50\" : \"\"\r\n                    }`}\r\n                  >\r\n                    {skill.medium}\r\n                  </TableCell>\r\n                  <TableCell className=\"w-[90px] capitalize  py-3 sticky right-[0px] bg-white pl-4\">\r\n                    {skill.medium_match_availibility_count ?? 0}\r\n                  </TableCell>\r\n                  <TableCell className=\"w-[90px] capitalize  py-3 sticky right-[0px] bg-white pl-4\">\r\n                    {skill.medium_match_address_count ?? 0}\r\n                  </TableCell>\r\n                </TableRow>\r\n              ))}\r\n            </TableBody>\r\n          </Table>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;;;;;AAEe,SAAS,gBAAgB,EACtC,IAAI,EACJ,UAAU,EACV,OAAO,EAKR;IACC,oEAAoE;IACpE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;oBAAC,WAAU;8BACf,cAAA,6LAAC,6HAAA,CAAA,cAAW;kCACV,cAAA,6LAAC,6HAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6LAAC,6HAAA,CAAA,YAAS;oCACR,WAAU;oCACV,SAAS,IAAM,WAAW;;wCAC3B;sDACa,6LAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAErC,6LAAC,6HAAA,CAAA,YAAS;oCACR,WAAU;oCACV,SAAS,IAAM,WAAW;;wCAC3B;sDACc,6LAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEtC,6LAAC,6HAAA,CAAA,YAAS;oCACR,WAAU;oCACV,SAAS,IAAM,WAAW;;wCAC3B;sDAEC,6LAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,6HAAA,CAAA,YAAS;oCACR,WAAU;oCACV,SAAS,IAAM,WAAW;;wCAC3B;sDAEC,6LAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,6HAAA,CAAA,YAAS;oCACR,WAAU;oCACV,SAAS,IAAM,WAAW;;wCAC3B;sDAEC,6LAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,6HAAA,CAAA,YAAS;oCACR,WAAU;oCACV,SAAS,IAAM,WAAW;;wCAC3B;sDAEC,6LAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,6HAAA,CAAA,YAAS;oCACR,WAAU;oCACV,SAAS,IAAM,WAAW;;wCAC3B;sDAEC,6LAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;8CAEzB,6LAAC,6HAAA,CAAA,YAAS;oCACR,WAAU;oCACV,SAAS,IAAM,WAAW;;wCAC3B;sDAEC,6LAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQhC,wBACC,6LAAC,yHAAA,CAAA,UAAO;gBAAC,QAAO;;;;;qCAEhB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;oBAAC,WAAU;8BACf,cAAA,6LAAC,6HAAA,CAAA,YAAS;kCACP,KAAK,GAAG,CAAC,CAAC,OAAO,oBAChB,6LAAC,6HAAA,CAAA,WAAQ;gCAEP,WAAW,GAAG,MAAM,MAAM,IAAI,eAAe,IAAI;;kDAEjD,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDAClB,MAAM,WAAW;;;;;;kDAEpB,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDAClB,MAAM,QAAQ,kBAAI,6LAAC;4CAAE,WAAU;sDAAO;;;;;;;;;;;kDAEzC,6LAAC,6HAAA,CAAA,YAAS;wCACR,WAAW,CAAC,+CAA+C,EACzD,MAAM,MAAM,IAAI,eAAe,IAC/B;kDAED,MAAM,IAAI;;;;;;kDAEb,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDAClB,MAAM,6BAA6B,IAAI;;;;;;kDAE1C,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDAClB,MAAM,wBAAwB,IAAI;;;;;;kDAErC,6LAAC,6HAAA,CAAA,YAAS;wCACR,WAAW,CAAC,iDAAiD,EAC3D,MAAM,MAAM,IAAI,eAAe,IAC/B;kDAED,MAAM,MAAM;;;;;;kDAEf,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDAClB,MAAM,+BAA+B,IAAI;;;;;;kDAE5C,6LAAC,6HAAA,CAAA,YAAS;wCAAC,WAAU;kDAClB,MAAM,0BAA0B,IAAI;;;;;;;+BAjClC;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CvB;KAhIwB"}}, {"offset": {"line": 881, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 887, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/workforce-index/GroupedSubCategoriesTable.tsx"], "sourcesContent": ["import {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from \"@/components/ui/table\";\r\nimport React, { useState } from \"react\";\r\nimport { ArrowUpDown, ChevronDown, ChevronRight } from \"lucide-react\";\r\nimport { SubCategoryPool } from \"@/app/workforce-index/page\";\r\n\r\ntype SortKey = keyof SubCategoryPool;\r\ntype SortDirection = \"asc\" | \"desc\";\r\n\r\ntype NumericField = keyof Omit<SubCategoryPool, \"category\" | \"subcategory\">;\r\n\r\nconst numericFields: NumericField[] = [\r\n  \"high\",\r\n  \"high_match_availibility_count\",\r\n  \"high_match_address_count\",\r\n  \"medium\",\r\n  \"medium_match_availibility_count\",\r\n  \"medium_match_address_count\",\r\n];\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nconst ColumnMapping: any = {\r\n  high: \"High Matches\",\r\n  high_match_availibility_count: \"High Match Availability Count\",\r\n  high_match_address_count: \"High Match Address Count\",\r\n  medium: \"Medium Matches\",\r\n  medium_match_availibility_count: \"Medium Match Availability Count\",\r\n  medium_match_address_count: \"Medium Match Address count\",\r\n};\r\n\r\ntype SortableSubField = NumericField | \"subcategory\";\r\n\r\nexport default function GroupedSubCategoriesTable({\r\n  data,\r\n}: {\r\n  data: SubCategoryPool[];\r\n}) {\r\n  const [expanded, setExpanded] = useState<Record<string, boolean>>({});\r\n  const [sortColumn, setSortColumn] = useState<SortKey>(\"category\");\r\n  const [sortDirection, setSortDirection] = useState<SortDirection>(\"asc\");\r\n  const [subSortConfig, setSubSortConfig] = useState<\r\n    Record<string, { column: SortableSubField; direction: SortDirection }>\r\n  >({});\r\n\r\n  const grouped = data.reduce((acc, item) => {\r\n    if (!acc[item.category]) acc[item.category] = [];\r\n    acc[item.category].push(item);\r\n    return acc;\r\n  }, {} as Record<string, SubCategoryPool[]>);\r\n\r\n  const toggleRow = (category: string) => {\r\n    setExpanded((prev) => ({ ...prev, [category]: !prev[category] }));\r\n  };\r\n\r\n  const calculateSums = (rows: SubCategoryPool[]): Record<string, number> => {\r\n    const result: Record<string, number> = {};\r\n    for (const field of numericFields) {\r\n      result[field] = rows.reduce((sum, row) => sum + (row[field] ?? 0), 0);\r\n    }\r\n    return result;\r\n  };\r\n\r\n  const handleSort = (key: SortKey) => {\r\n    if (sortColumn === key) {\r\n      setSortDirection((prev) => (prev === \"asc\" ? \"desc\" : \"asc\"));\r\n    } else {\r\n      setSortColumn(key);\r\n      setSortDirection(\"asc\");\r\n    }\r\n  };\r\n\r\n  const handleSubSort = (category: string, key: SortableSubField) => {\r\n    setSubSortConfig((prev) => {\r\n      const current = prev[category];\r\n      if (current?.column === key) {\r\n        return {\r\n          ...prev,\r\n          [category]: {\r\n            column: key,\r\n            direction: current.direction === \"asc\" ? \"desc\" : \"asc\",\r\n          },\r\n        };\r\n      } else {\r\n        return {\r\n          ...prev,\r\n          [category]: { column: key, direction: \"asc\" },\r\n        };\r\n      }\r\n    });\r\n  };\r\n\r\n  const sortedGrouped = Object.entries(grouped).sort(\r\n    ([catA, rowsA], [catB, rowsB]) => {\r\n      if (!sortColumn) return 0;\r\n\r\n      if (sortColumn === \"category\") {\r\n        return sortDirection === \"asc\"\r\n          ? catA.localeCompare(catB)\r\n          : catB.localeCompare(catA);\r\n      }\r\n\r\n      const sumA = calculateSums(rowsA)[sortColumn] ?? 0;\r\n      const sumB = calculateSums(rowsB)[sortColumn] ?? 0;\r\n      return sortDirection === \"asc\" ? sumA - sumB : sumB - sumA;\r\n    }\r\n  );\r\n\r\n  const getWidth = (key: string) => {\r\n    const widths: Record<string, string> = {\r\n      category: \"w-[400px]\",\r\n      high_match_address_count: \"w-[90px]\",\r\n      medium_match_address_count: \"w-[90px]\",\r\n      high_match_availibility_count: \"w-[90px]\",\r\n      medium_match_availibility_count: \"w-[90px]\",\r\n      optimal_count: \"w-[90px]\",\r\n      medium: \"w-[90px]\",\r\n      high: \"w-[95px]\",\r\n    };\r\n    return widths[key] ?? \"w-[90px]\";\r\n  };\r\n\r\n  return (\r\n    <div className=\"border rounded-lg overflow-hidden\">\r\n      <Table className=\"w-full\">\r\n        <TableHeader>\r\n          <TableRow className=\"bg-gray-900 hover:bg-gray-900 text-white\">\r\n            <TableHead\r\n              className={`cursor-pointer ${getWidth(\r\n                \"category\"\r\n              )} px-4 py-3 text-white`}\r\n              onClick={() => handleSort(\"category\")}\r\n            >\r\n              Go To Market\r\n              <ArrowUpDown className=\"inline ml-1 h-4 w-4\" />\r\n            </TableHead>\r\n            {numericFields.map((field) => (\r\n              <TableHead\r\n                key={field}\r\n                className={`cursor-pointer ${getWidth(\r\n                  field\r\n                )} py-3 px-5 text-white sticky`}\r\n                onClick={() => handleSort(field)}\r\n              >\r\n                <span className=\"\">{ColumnMapping[field]}</span>\r\n                <ArrowUpDown className=\"inline ml-1 h-4 w-4\" />\r\n              </TableHead>\r\n            ))}\r\n          </TableRow>\r\n        </TableHeader>\r\n      </Table>\r\n\r\n      <div className=\"max-h-[37vh] overflow-auto\">\r\n        <Table className=\"w-full\">\r\n          <TableBody>\r\n            {sortedGrouped.map(([category, rows]) => {\r\n              const sums = calculateSums(rows);\r\n              const sortedRows = [...rows];\r\n\r\n              const currentSort = subSortConfig[category];\r\n              if (expanded[category] && currentSort?.column) {\r\n                sortedRows.sort((a, b) => {\r\n                  const { column, direction } = currentSort;\r\n\r\n                  if (column === \"subcategory\") {\r\n                    return direction === \"asc\"\r\n                      ? a.subcategory.localeCompare(b.subcategory)\r\n                      : b.subcategory.localeCompare(a.subcategory);\r\n                  }\r\n\r\n                  return direction === \"asc\"\r\n                    ? (a[column] ?? 0) - (b[column] ?? 0)\r\n                    : (b[column] ?? 0) - (a[column] ?? 0);\r\n                });\r\n              }\r\n\r\n              return (\r\n                <React.Fragment key={category}>\r\n                  <TableRow\r\n                    className=\"bg-gray-100 cursor-pointer\"\r\n                    onClick={() => toggleRow(category)}\r\n                  >\r\n                    <TableCell className={getWidth(\"category\")}>\r\n                      <span className=\"font-semibold flex\">\r\n                        <span className=\"text-[20px] font-normal\">\r\n                          {expanded[category] ? (\r\n                            <ChevronDown size={18} />\r\n                          ) : (\r\n                            <ChevronRight size={18} />\r\n                          )}\r\n                        </span>{\" \"}\r\n                        {category}\r\n                      </span>\r\n                    </TableCell>\r\n                    {numericFields.map((field) => (\r\n                      <TableCell\r\n                        key={field}\r\n                        className={`${getWidth(field)} ${\r\n                          expanded[category] ? \"px-4\" : \"px-4\"\r\n                        }`}\r\n                      >\r\n                        {sums[field] ?? 0}\r\n                      </TableCell>\r\n                    ))}\r\n                  </TableRow>\r\n\r\n                  {expanded[category] && (\r\n                    <>\r\n                      <TableRow className=\"bg-gray-50 text-sm font-semibold text-gray-600\">\r\n                        <TableCell\r\n                          onClick={() => handleSubSort(category, \"subcategory\")}\r\n                          className={`pl-8 cursor-pointer ${getWidth(\r\n                            \"category\"\r\n                          )}`}\r\n                        >\r\n                          Subcategory\r\n                          <ArrowUpDown className=\"size-3 inline-block ml-1\" />\r\n                        </TableCell>\r\n                        {numericFields.map((field) => (\r\n                          <TableCell\r\n                            key={field}\r\n                            className={`px-4 cursor-pointer ${getWidth(field)}`}\r\n                            onClick={() => handleSubSort(category, field)}\r\n                          >\r\n                            {ColumnMapping[field]}\r\n                            <ArrowUpDown className=\"size-3 inline-block ml-1\" />\r\n                          </TableCell>\r\n                        ))}\r\n                      </TableRow>\r\n\r\n                      {sortedRows.map((sub, i) => (\r\n                        <TableRow\r\n                          key={`${category}-${i}`}\r\n                          className=\"bg-white text-sm\"\r\n                        >\r\n                          <TableCell\r\n                            className={`pl-8 italic ${getWidth(\"category\")}`}\r\n                          >\r\n                            {sub.subcategory}\r\n                          </TableCell>\r\n                          {numericFields.map((field) => (\r\n                            <TableCell\r\n                              key={field}\r\n                              className={`${getWidth(field)} px-4`}\r\n                            >\r\n                              {sub[field] ?? 0}\r\n                            </TableCell>\r\n                          ))}\r\n                        </TableRow>\r\n                      ))}\r\n                    </>\r\n                  )}\r\n                </React.Fragment>\r\n              );\r\n            })}\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAQA;AACA;AAAA;AAAA;;;;;;AAQA,MAAM,gBAAgC;IACpC;IACA;IACA;IACA;IACA;IACA;CACD;AAED,8DAA8D;AAC9D,MAAM,gBAAqB;IACzB,MAAM;IACN,+BAA+B;IAC/B,0BAA0B;IAC1B,QAAQ;IACR,iCAAiC;IACjC,4BAA4B;AAC9B;AAIe,SAAS,0BAA0B,EAChD,IAAI,EAGL;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACnE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAE/C,CAAC;IAEH,MAAM,UAAU,KAAK,MAAM,CAAC,CAAC,KAAK;QAChC,IAAI,CAAC,GAAG,CAAC,KAAK,QAAQ,CAAC,EAAE,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG,EAAE;QAChD,GAAG,CAAC,KAAK,QAAQ,CAAC,CAAC,IAAI,CAAC;QACxB,OAAO;IACT,GAAG,CAAC;IAEJ,MAAM,YAAY,CAAC;QACjB,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC,SAAS;YAAC,CAAC;IACjE;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAiC,CAAC;QACxC,KAAK,MAAM,SAAS,cAAe;YACjC,MAAM,CAAC,MAAM,GAAG,KAAK,MAAM,CAAC,CAAC,KAAK,MAAQ,MAAM,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,GAAG;QACrE;QACA,OAAO;IACT;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,eAAe,KAAK;YACtB,iBAAiB,CAAC,OAAU,SAAS,QAAQ,SAAS;QACxD,OAAO;YACL,cAAc;YACd,iBAAiB;QACnB;IACF;IAEA,MAAM,gBAAgB,CAAC,UAAkB;QACvC,iBAAiB,CAAC;YAChB,MAAM,UAAU,IAAI,CAAC,SAAS;YAC9B,IAAI,SAAS,WAAW,KAAK;gBAC3B,OAAO;oBACL,GAAG,IAAI;oBACP,CAAC,SAAS,EAAE;wBACV,QAAQ;wBACR,WAAW,QAAQ,SAAS,KAAK,QAAQ,SAAS;oBACpD;gBACF;YACF,OAAO;gBACL,OAAO;oBACL,GAAG,IAAI;oBACP,CAAC,SAAS,EAAE;wBAAE,QAAQ;wBAAK,WAAW;oBAAM;gBAC9C;YACF;QACF;IACF;IAEA,MAAM,gBAAgB,OAAO,OAAO,CAAC,SAAS,IAAI,CAChD,CAAC,CAAC,MAAM,MAAM,EAAE,CAAC,MAAM,MAAM;QAC3B,IAAI,CAAC,YAAY,OAAO;QAExB,IAAI,eAAe,YAAY;YAC7B,OAAO,kBAAkB,QACrB,KAAK,aAAa,CAAC,QACnB,KAAK,aAAa,CAAC;QACzB;QAEA,MAAM,OAAO,cAAc,MAAM,CAAC,WAAW,IAAI;QACjD,MAAM,OAAO,cAAc,MAAM,CAAC,WAAW,IAAI;QACjD,OAAO,kBAAkB,QAAQ,OAAO,OAAO,OAAO;IACxD;IAGF,MAAM,WAAW,CAAC;QAChB,MAAM,SAAiC;YACrC,UAAU;YACV,0BAA0B;YAC1B,4BAA4B;YAC5B,+BAA+B;YAC/B,iCAAiC;YACjC,eAAe;YACf,QAAQ;YACR,MAAM;QACR;QACA,OAAO,MAAM,CAAC,IAAI,IAAI;IACxB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,6HAAA,CAAA,QAAK;gBAAC,WAAU;0BACf,cAAA,6LAAC,6HAAA,CAAA,cAAW;8BACV,cAAA,6LAAC,6HAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,6HAAA,CAAA,YAAS;gCACR,WAAW,CAAC,eAAe,EAAE,SAC3B,YACA,qBAAqB,CAAC;gCACxB,SAAS,IAAM,WAAW;;oCAC3B;kDAEC,6LAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;4BAExB,cAAc,GAAG,CAAC,CAAC,sBAClB,6LAAC,6HAAA,CAAA,YAAS;oCAER,WAAW,CAAC,eAAe,EAAE,SAC3B,OACA,4BAA4B,CAAC;oCAC/B,SAAS,IAAM,WAAW;;sDAE1B,6LAAC;4CAAK,WAAU;sDAAI,aAAa,CAAC,MAAM;;;;;;sDACxC,6LAAC,2NAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;mCAPlB;;;;;;;;;;;;;;;;;;;;;0BAcf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;oBAAC,WAAU;8BACf,cAAA,6LAAC,6HAAA,CAAA,YAAS;kCACP,cAAc,GAAG,CAAC,CAAC,CAAC,UAAU,KAAK;4BAClC,MAAM,OAAO,cAAc;4BAC3B,MAAM,aAAa;mCAAI;6BAAK;4BAE5B,MAAM,cAAc,aAAa,CAAC,SAAS;4BAC3C,IAAI,QAAQ,CAAC,SAAS,IAAI,aAAa,QAAQ;gCAC7C,WAAW,IAAI,CAAC,CAAC,GAAG;oCAClB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG;oCAE9B,IAAI,WAAW,eAAe;wCAC5B,OAAO,cAAc,QACjB,EAAE,WAAW,CAAC,aAAa,CAAC,EAAE,WAAW,IACzC,EAAE,WAAW,CAAC,aAAa,CAAC,EAAE,WAAW;oCAC/C;oCAEA,OAAO,cAAc,QACjB,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,IAClC,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC;gCACxC;4BACF;4BAEA,qBACE,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;kDACb,6LAAC,6HAAA,CAAA,WAAQ;wCACP,WAAU;wCACV,SAAS,IAAM,UAAU;;0DAEzB,6LAAC,6HAAA,CAAA,YAAS;gDAAC,WAAW,SAAS;0DAC7B,cAAA,6LAAC;oDAAK,WAAU;;sEACd,6LAAC;4DAAK,WAAU;sEACb,QAAQ,CAAC,SAAS,iBACjB,6LAAC,uNAAA,CAAA,cAAW;gEAAC,MAAM;;;;;qFAEnB,6LAAC,yNAAA,CAAA,eAAY;gEAAC,MAAM;;;;;;;;;;;wDAEhB;wDACP;;;;;;;;;;;;4CAGJ,cAAc,GAAG,CAAC,CAAC,sBAClB,6LAAC,6HAAA,CAAA,YAAS;oDAER,WAAW,GAAG,SAAS,OAAO,CAAC,EAC7B,QAAQ,CAAC,SAAS,GAAG,SAAS,QAC9B;8DAED,IAAI,CAAC,MAAM,IAAI;mDALX;;;;;;;;;;;oCAUV,QAAQ,CAAC,SAAS,kBACjB;;0DACE,6LAAC,6HAAA,CAAA,WAAQ;gDAAC,WAAU;;kEAClB,6LAAC,6HAAA,CAAA,YAAS;wDACR,SAAS,IAAM,cAAc,UAAU;wDACvC,WAAW,CAAC,oBAAoB,EAAE,SAChC,aACC;;4DACJ;0EAEC,6LAAC,2NAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;;oDAExB,cAAc,GAAG,CAAC,CAAC,sBAClB,6LAAC,6HAAA,CAAA,YAAS;4DAER,WAAW,CAAC,oBAAoB,EAAE,SAAS,QAAQ;4DACnD,SAAS,IAAM,cAAc,UAAU;;gEAEtC,aAAa,CAAC,MAAM;8EACrB,6LAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;;2DALlB;;;;;;;;;;;4CAUV,WAAW,GAAG,CAAC,CAAC,KAAK,kBACpB,6LAAC,6HAAA,CAAA,WAAQ;oDAEP,WAAU;;sEAEV,6LAAC,6HAAA,CAAA,YAAS;4DACR,WAAW,CAAC,YAAY,EAAE,SAAS,aAAa;sEAE/C,IAAI,WAAW;;;;;;wDAEjB,cAAc,GAAG,CAAC,CAAC,sBAClB,6LAAC,6HAAA,CAAA,YAAS;gEAER,WAAW,GAAG,SAAS,OAAO,KAAK,CAAC;0EAEnC,GAAG,CAAC,MAAM,IAAI;+DAHV;;;;;;mDAVJ,GAAG,SAAS,CAAC,EAAE,GAAG;;;;;;;;+BAvDZ;;;;;wBA6EzB;;;;;;;;;;;;;;;;;;;;;;AAMZ;GAnOwB;KAAA"}}, {"offset": {"line": 1250, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1256, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/utils/withEntitlementCheck.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEntitlement } from \"@/context/EntitlementContext\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useEffect, useState } from \"react\";\r\n\r\nexport function withEntitlementCheck(\r\n  Component: React.ComponentType,\r\n  key: string\r\n) {\r\n  return function WrappedComponent(props: any) {\r\n    const { entitlements } = useEntitlement();\r\n    const router = useRouter();\r\n    const [entitlementChecked, setEntitlementChecked] = useState(false);\r\n\r\n    useEffect(() => {\r\n      if (!entitlements || Object.keys(entitlements).length === 0) return;\r\n\r\n      if (entitlements[key] === false) {\r\n        router.push(\"/\");\r\n      } else {\r\n        setEntitlementChecked(true); // Only render when check is complete\r\n      }\r\n    }, [entitlements, router]);\r\n\r\n    // Prevent premature render until entitlement is checked\r\n    if (!entitlementChecked) return null;\r\n\r\n    return <Component {...props} />;\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMO,SAAS,qBACd,SAA8B,EAC9B,GAAW;;IAEX,UAAO,SAAS,iBAAiB,KAAU;;QACzC,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;QACtC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;QACvB,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+DAAE;gBACR,IAAI,CAAC,gBAAgB,OAAO,IAAI,CAAC,cAAc,MAAM,KAAK,GAAG;gBAE7D,IAAI,YAAY,CAAC,IAAI,KAAK,OAAO;oBAC/B,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,sBAAsB,OAAO,qCAAqC;gBACpE;YACF;8DAAG;YAAC;YAAc;SAAO;QAEzB,wDAAwD;QACxD,IAAI,CAAC,oBAAoB,OAAO;QAEhC,qBAAO,6LAAC;YAAW,GAAG,KAAK;;;;;;IAC7B;;YAlB2B,iIAAA,CAAA,iBAAc;YACxB,qIAAA,CAAA,YAAS;;;AAkB5B"}}, {"offset": {"line": 1307, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1313, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/app/workforce-index/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect, useState } from \"react\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport StatsSection from \"@/components/pools/StatsSection\";\r\nimport { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from \"@/components/ui/tabs\";\r\nimport Loading from \"@/components/Loading\";\r\nimport CategoriesTable from \"@/components/workforce-index/CategoriesTable\";\r\nimport GroupedSubCategoriesTable from \"@/components/workforce-index/GroupedSubCategoriesTable\";\r\nimport { withEntitlementCheck } from \"@/utils/withEntitlementCheck\";\r\nimport { trackedFetch } from \"@/library/trackApi\";\r\nimport { getAppInsights } from \"@/library/appInsights\";\r\n\r\nexport interface SubCategoryPool {\r\n  subcategory: string;\r\n  category: string;\r\n  high: number;\r\n  medium: number;\r\n  low: number;\r\n  optimal_count: number;\r\n  unknown: number;\r\n  other: number;\r\n  high_match_address_count: number;\r\n  medium_match_address_count: number;\r\n  high_match_availibility_count: number;\r\n  medium_match_availibility_count: number;\r\n}\r\n\r\nexport interface SubCategory {\r\n  id: number;\r\n  name: string;\r\n  category_id: number;\r\n  category_name: string;\r\n}\r\n\r\nfunction SubcategoryPools() {\r\n  const [data, setData] = useState<SubCategoryPool[]>([]);\r\n  const [filteredData, setFilteredData] = useState<SubCategoryPool[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [search, setSearch] = useState(\"\");\r\n  const [sortConfig, setSortConfig] = useState<{\r\n    key: keyof SubCategoryPool | null;\r\n    direction: \"asc\" | \"desc\" | null;\r\n  }>({\r\n    key: null,\r\n    direction: null,\r\n  });\r\n\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const responseSubCategories = await trackedFetch(\r\n          `/api/subcategories`,\r\n          {},\r\n          { context: \"Subcategories\" }\r\n        );\r\n        const subCategories: SubCategory[] = await responseSubCategories.json();\r\n\r\n        const response = await trackedFetch(\r\n          `/api/pools`,\r\n          {},\r\n          { context: \"Pools\" }\r\n        );\r\n\r\n        if (response.ok) {\r\n          const data: SubCategoryPool[] = await response.json();\r\n          const updatedData = data.map((item) => {\r\n            const subCategoryMatch = subCategories.find(\r\n              (sub) => sub.name === item.subcategory\r\n            );\r\n            return {\r\n              ...item,\r\n              category: subCategoryMatch ? subCategoryMatch.category_name : \"\",\r\n            };\r\n          });\r\n\r\n          setData(updatedData);\r\n          setFilteredData(updatedData);\r\n          getAppInsights()?.trackEvent({\r\n            name: \"FE_Workforce_Index_Fetched\",\r\n          });\r\n        } else {\r\n          console.error(\"Failed to fetch data:\", response.statusText);\r\n        }\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n      } catch (error: any) {\r\n        console.error(\"Error fetching data:\", error);\r\n        getAppInsights()?.trackException({\r\n          error: new Error(\"Workforce Index API error: \" + error?.message),\r\n          severityLevel: 3,\r\n        });\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    fetchData();\r\n  }, []);\r\n\r\n  // Filter Data based on Search\r\n  useEffect(() => {\r\n    const filtered = data.filter((item) =>\r\n      item.subcategory.toLowerCase().includes(search.toLowerCase())\r\n    );\r\n    setFilteredData(filtered);\r\n  }, [search, data]);\r\n\r\n  // Handle Sorting\r\n  const handleSort = (key: keyof SubCategoryPool) => {\r\n    let direction: \"asc\" | \"desc\" | null = \"asc\";\r\n    if (sortConfig.key === key && sortConfig.direction === \"asc\") {\r\n      direction = \"desc\";\r\n    }\r\n\r\n    const sortedData = [...filteredData].sort((a, b) => {\r\n      if (a[key] < b[key]) return direction === \"asc\" ? -1 : 1;\r\n      if (a[key] > b[key]) return direction === \"asc\" ? 1 : -1;\r\n      return 0;\r\n    });\r\n\r\n    setSortConfig({ key, direction });\r\n    setFilteredData(sortedData);\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full lg:w-[1260px] 2xl:w-[95%] mx-auto mt-6 p-5 bg-white shadow-lg rounded-lg\">\r\n      <h2 className=\"text-2xl font-bold mb-5 text-center capitalize flex items-center justify-center\">\r\n        Workforce Readiness Index\r\n      </h2>\r\n      {/* commented for feature implementation */}\r\n      <StatsSection />\r\n      {/* Search Input */}\r\n      {/* Table Container */}\r\n      <Tabs defaultValue=\"grouped\" className=\"w-full\">\r\n        <TabsList className=\"grid grid-cols-2 w-[400px] mx-auto mb-5 mt-5\">\r\n          <TabsTrigger value=\"grouped\">Go To Market</TabsTrigger>\r\n          <TabsTrigger value=\"categories\">All Sub-Categories</TabsTrigger>\r\n        </TabsList>\r\n\r\n        <TabsContent value=\"categories\">\r\n          <div className=\"mb-4 mt-5 flex justify-between items-center\">\r\n            <Input\r\n              type=\"text\"\r\n              placeholder=\"Search Subcategory...\"\r\n              value={search}\r\n              onChange={(e) => setSearch(e.target.value)}\r\n              className=\"w-1/3 border border-gray-300 rounded-lg p-2\"\r\n            />\r\n          </div>\r\n          {loading ? (\r\n            <Loading height=\"h-[37vh]\" />\r\n          ) : (\r\n            <CategoriesTable\r\n              handleSort={handleSort}\r\n              loading={loading}\r\n              data={filteredData}\r\n            />\r\n          )}\r\n        </TabsContent>\r\n\r\n        <TabsContent value=\"grouped\">\r\n          {loading ? (\r\n            <Loading height=\"h-[37vh]\" />\r\n          ) : (\r\n            <GroupedSubCategoriesTable data={filteredData} />\r\n          )}\r\n        </TabsContent>\r\n      </Tabs>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default withEntitlementCheck(SubcategoryPools, \"Work_force_Index\");\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAmCA,SAAS;;IACP,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAGxC;QACD,KAAK;QACL,WAAW;IACb;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;wDAAY;oBAChB,IAAI;wBACF,WAAW;wBACX,MAAM,wBAAwB,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAC7C,CAAC,kBAAkB,CAAC,EACpB,CAAC,GACD;4BAAE,SAAS;wBAAgB;wBAE7B,MAAM,gBAA+B,MAAM,sBAAsB,IAAI;wBAErE,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAChC,CAAC,UAAU,CAAC,EACZ,CAAC,GACD;4BAAE,SAAS;wBAAQ;wBAGrB,IAAI,SAAS,EAAE,EAAE;4BACf,MAAM,OAA0B,MAAM,SAAS,IAAI;4BACnD,MAAM,cAAc,KAAK,GAAG;oFAAC,CAAC;oCAC5B,MAAM,mBAAmB,cAAc,IAAI;6GACzC,CAAC,MAAQ,IAAI,IAAI,KAAK,KAAK,WAAW;;oCAExC,OAAO;wCACL,GAAG,IAAI;wCACP,UAAU,mBAAmB,iBAAiB,aAAa,GAAG;oCAChE;gCACF;;4BAEA,QAAQ;4BACR,gBAAgB;4BAChB,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;gCAC3B,MAAM;4BACR;wBACF,OAAO;4BACL,QAAQ,KAAK,CAAC,yBAAyB,SAAS,UAAU;wBAC5D;oBACA,8DAA8D;oBAChE,EAAE,OAAO,OAAY;wBACnB,QAAQ,KAAK,CAAC,wBAAwB;wBACtC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;4BAC/B,OAAO,IAAI,MAAM,gCAAgC,OAAO;4BACxD,eAAe;wBACjB;oBACF,SAAU;wBACR,WAAW;oBACb;gBACF;;YACA;QACF;qCAAG,EAAE;IAEL,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,WAAW,KAAK,MAAM;uDAAC,CAAC,OAC5B,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO,WAAW;;YAE5D,gBAAgB;QAClB;qCAAG;QAAC;QAAQ;KAAK;IAEjB,iBAAiB;IACjB,MAAM,aAAa,CAAC;QAClB,IAAI,YAAmC;QACvC,IAAI,WAAW,GAAG,KAAK,OAAO,WAAW,SAAS,KAAK,OAAO;YAC5D,YAAY;QACd;QAEA,MAAM,aAAa;eAAI;SAAa,CAAC,IAAI,CAAC,CAAC,GAAG;YAC5C,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,OAAO,cAAc,QAAQ,CAAC,IAAI;YACvD,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,OAAO,cAAc,QAAQ,IAAI,CAAC;YACvD,OAAO;QACT;QAEA,cAAc;YAAE;YAAK;QAAU;QAC/B,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAkF;;;;;;0BAIhG,6LAAC,uIAAA,CAAA,UAAY;;;;;0BAGb,6LAAC,4HAAA,CAAA,OAAI;gBAAC,cAAa;gBAAU,WAAU;;kCACrC,6LAAC,4HAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,6LAAC,4HAAA,CAAA,cAAW;gCAAC,OAAM;0CAAU;;;;;;0CAC7B,6LAAC,4HAAA,CAAA,cAAW;gCAAC,OAAM;0CAAa;;;;;;;;;;;;kCAGlC,6LAAC,4HAAA,CAAA,cAAW;wBAAC,OAAM;;0CACjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6HAAA,CAAA,QAAK;oCACJ,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;oCACzC,WAAU;;;;;;;;;;;4BAGb,wBACC,6LAAC,yHAAA,CAAA,UAAO;gCAAC,QAAO;;;;;qDAEhB,6LAAC,uJAAA,CAAA,UAAe;gCACd,YAAY;gCACZ,SAAS;gCACT,MAAM;;;;;;;;;;;;kCAKZ,6LAAC,4HAAA,CAAA,cAAW;wBAAC,OAAM;kCAChB,wBACC,6LAAC,yHAAA,CAAA,UAAO;4BAAC,QAAO;;;;;iDAEhB,6LAAC,iKAAA,CAAA,UAAyB;4BAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;;AAM7C;GAvIS;KAAA;6CAyIM,CAAA,GAAA,iIAAA,CAAA,uBAAoB,AAAD,EAAE,kBAAkB"}}, {"offset": {"line": 1559, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}