module.exports = {

"[externals]/next/dist/compiled/next-server/app-route.runtime.dev.js [external] (next/dist/compiled/next-server/app-route.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/compiled/next-server/app-route.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/compiled/next-server/app-page.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/api/config.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "API_ENDPOINTS": (()=>API_ENDPOINTS),
    "EXPERIMENT_BASE_URL": (()=>EXPERIMENT_BASE_URL),
    "IS_LOCK_FEATURE_DISABLED": (()=>IS_LOCK_FEATURE_DISABLED),
    "IS_WHY_FIT_EDITABLE": (()=>IS_WHY_FIT_EDITABLE),
    "isADLogin": (()=>isADLogin),
    "isParsedResume": (()=>isParsedResume)
});
const BASE_URL = ("TURBOPACK compile-time value", "http://0.0.0.0:8005") || "http://0.0.0.0:8005";
const PORTAL_SERVICE_BASE_URL = process.env.DP_PORTAL_SERVICE || "http://0.0.0.0:8006";
const EXPERIMENT_BASE_URL = ("TURBOPACK compile-time value", "http://0.0.0.0:8005") || "http://localhost:8005";
const isADLogin = ()=>("TURBOPACK compile-time value", "true") === "true";
const isParsedResume = ("TURBOPACK compile-time value", "true") === "true";
const IS_WHY_FIT_EDITABLE = ("TURBOPACK compile-time value", "false");
const IS_LOCK_FEATURE_DISABLED = ("TURBOPACK compile-time value", "true");
const API_ENDPOINTS = {
    categories: `${BASE_URL}/categories`,
    subCategories: `${BASE_URL}/subcategories`,
    subCategoriesPools: `${BASE_URL}/subcategory/pools`,
    jobTitlesBySubCategory: `${BASE_URL}/jobtitles/:sub_category_id?limit=10000000`,
    deleteAttribute: `${BASE_URL}/attribute/delete/:attribute_id`,
    fetchAttributesBySubCategory: `${BASE_URL}/attributes/:sub_category_id?limit=10000000`,
    fetchWeightsBySubCategory: `${BASE_URL}/weights/:sub_category_id`,
    updateAttributeWeight: `${BASE_URL}/attributes/:sub_category_id/update`,
    updateSubcategoryOfAttribute: `${BASE_URL}/attributes/:attribute_id/subcategory`,
    updateAttributeApprovalStatus: `${BASE_URL}/attributes/:attribute_id/approval`,
    candidatesData: `${BASE_URL}/candidates`,
    updateCandidatesReviewData: `${BASE_URL}/candidates/update_in_db`,
    jobsData: `${BASE_URL}/jobs`,
    getVacancies: `${BASE_URL}/vacancies`,
    getVacanciesFromFiles: `${BASE_URL}/files/vacancies`,
    getCandidatesByVacancyId: `${BASE_URL}/candidates/:vacancy_id`,
    getResumeByContactId: `${BASE_URL}/resume/:contact_id`,
    getResumeFromFileByContactId: `${BASE_URL}/files/candidate-resume/:contact_id`,
    getAllSubcategoryWeightConfigs: `${BASE_URL}/v1/subcategory/weight-configs`,
    updateSubcategoryWeightConfig: `${BASE_URL}/v1/subcategory/weight-configs/:subcategory_id`,
    getCandidateStats: `${BASE_URL}/api/candidate-stats`,
    getEntitlements: `${PORTAL_SERVICE_BASE_URL}/api/entitlement`,
    getVacancyConfigAndStatus: `${BASE_URL}/vacancies/:vacancy_id/status`,
    startVacancyReview: `${BASE_URL}/vacancies/:vacancy_id/start`,
    completeVacancyReview: `${BASE_URL}/vacancies/:vacancy_id/complete`,
    updateWhyFitData: `${BASE_URL}/candidates/fitness_reason`,
    vacanciesShortlisted: `${BASE_URL}/vacancies/shortlist`,
    saveHistoryLogs: `${PORTAL_SERVICE_BASE_URL}/api/add_historical_data?email_id={email_id}&portal_name={portal_name}&feature={feature}`,
    // New Experiment Endpoints
    experimentGetVacancies: `${EXPERIMENT_BASE_URL}/experiment/vacancies`,
    experimentGetVacancyRunDetails: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/details`,
    experimentGetCandidatesForVacancyRun: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/candidates`,
    experimentGetRunConfig: `${EXPERIMENT_BASE_URL}/experiment/runs/:run_id/config`,
    experimentGetCandidateResume: `${EXPERIMENT_BASE_URL}/experiment/candidates/:contact_id/resume`,
    experimentArchiveVacancy: `${EXPERIMENT_BASE_URL}/experiment/vacancies/archive`,
    experimentPromoteResults: `${EXPERIMENT_BASE_URL}/experiment/results/promote`
};
}}),
"[externals]/util [external] (util, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/path [external] (path, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("path", () => require("path"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/fs [external] (fs, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/assert [external] (assert, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("assert", () => require("assert"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("events", () => require("events"));

module.exports = mod;
}}),
"[project]/api/post.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "postData": (()=>postData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
const postData = async (url, data)=>{
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(url, {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            data
        });
        return response;
    } catch (error) {
        let message = error.response?.data?.detail?.message || error.response?.data?.error || error.message || "Unknown error";
        const err = new Error(message);
        err.name = "ApiError";
        // Attach extra info for advanced error handling
        err.status = error.response?.status || 500;
        err.data = error.response?.data ?? null;
        throw err;
    }
};
}}),
"[project]/api/put.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "updateData": (()=>updateData)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
const updateData = async (url, data)=>{
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])(url, {
            method: "PUT",
            headers: {
                "Content-Type": "application/json"
            },
            data
        });
        return response;
    } catch (error) {
        throw new Error(error.message);
    }
};
}}),
"[project]/api/serverActions.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// api/serverActions.ts
/* __next_internal_action_entry_do_not_use__ {"00430c3fe55aae2c16d8c5809c583e907f184f0dc7":"fetchVacancies","0070c516a719bc4d5da2991e171dfc1731b3bfcd01":"fetchAllCategories","0081f97a45fcc051f67f3f8979d95c4659f987c3e9":"getAllSubcategoryWeightConfigs","00dfd6ac6138fd6fdb4bb66cfe2e001b5b633b6248":"fetchAllSubCategories","00eed182c174bc0ad16b9dd64ee0f54ce7fdab93b8":"fetchVacanciesFromFiles","40001ab8f91b17607478f15f3e331f94fa8f492027":"fetchJobTitlesBySubCategory","402798ac62c44aa80cef8655242e6d58d9162b01ec":"fetchResumeByCandidateIdFromFiles","4058069912dbff67450c9f924a6c0aa1061b4c63d0":"fetchResumeByCandidateId","4063428e91071df462c0b9076728a7d32615a943e7":"fetchWeightsBySubcategoryId","406345071a46f57d39bc1d0cb464a8359afc40dca2":"updateRecruiterReview","406a3cc2b4fd2eb890eba0e64034b574ca766d629f":"postVacanciesShortlisted","406ac29c78d366f5e75ca4601810ebec39513dc73f":"deleteAttributeTitleById","408d6350adccefe97f8e569bca319fd5565d4ba3ab":"updateWhyFit","40b49559358c35ff4b692eb8eb12ea9614a4e1f71f":"fetchCandidatesByVacancyId","40d478d951619bce460cfbc7d49a3cc493ba4b47ea":"getVacancyConfigAndStatus","40fd2068e785dd85c91ddd23fd7f0468fa988fe738":"fetchAttributeBySubcategoryId","60177423cff0c80303d8847e8112cfa5122712a0f4":"updateSubcategoryWeightConfig","6027034db188ca657eb89968e241062f20d7184639":"completeVacancyreview","60494406d8979ee9809192155ba859d5ec85a89895":"updateAttributeApprovalStatus","6053d6f48110c08a061373b14ac7e907994228ab59":"fetchEntitlements","6094ec7632250ae73ee9964addbc280e43b1d90039":"updateAttributeWeight","60de454cb16c2ea8f0b7e2e25af8d0bfaa4de6e5bd":"updateSubcategoryOfAttribute","60ff2f47ca078fbac86668827367cfc9554ca27bc5":"startVacancyreview","7847e9e25707c39660f2fba6bfc09949a57236d8f3":"postHistoricalLogs"} */ __turbopack_esm__({
    "completeVacancyreview": (()=>completeVacancyreview),
    "deleteAttributeTitleById": (()=>deleteAttributeTitleById),
    "fetchAllCategories": (()=>fetchAllCategories),
    "fetchAllSubCategories": (()=>fetchAllSubCategories),
    "fetchAttributeBySubcategoryId": (()=>fetchAttributeBySubcategoryId),
    "fetchCandidatesByVacancyId": (()=>fetchCandidatesByVacancyId),
    "fetchEntitlements": (()=>fetchEntitlements),
    "fetchJobTitlesBySubCategory": (()=>fetchJobTitlesBySubCategory),
    "fetchResumeByCandidateId": (()=>fetchResumeByCandidateId),
    "fetchResumeByCandidateIdFromFiles": (()=>fetchResumeByCandidateIdFromFiles),
    "fetchVacancies": (()=>fetchVacancies),
    "fetchVacanciesFromFiles": (()=>fetchVacanciesFromFiles),
    "fetchWeightsBySubcategoryId": (()=>fetchWeightsBySubcategoryId),
    "getAllSubcategoryWeightConfigs": (()=>getAllSubcategoryWeightConfigs),
    "getVacancyConfigAndStatus": (()=>getVacancyConfigAndStatus),
    "postHistoricalLogs": (()=>postHistoricalLogs),
    "postVacanciesShortlisted": (()=>postVacanciesShortlisted),
    "startVacancyreview": (()=>startVacancyreview),
    "updateAttributeApprovalStatus": (()=>updateAttributeApprovalStatus),
    "updateAttributeWeight": (()=>updateAttributeWeight),
    "updateRecruiterReview": (()=>updateRecruiterReview),
    "updateSubcategoryOfAttribute": (()=>updateSubcategoryOfAttribute),
    "updateSubcategoryWeightConfig": (()=>updateSubcategoryWeightConfig),
    "updateWhyFit": (()=>updateWhyFit)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/app-render/encryption.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/headers.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/api/config.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$post$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/api/post.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$put$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/api/put.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-route] (ecmascript)");
;
;
;
;
;
;
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ fetchAllCategories() {
    try {
        const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].categories);
        const data = await response.json();
        return data?.categories || [];
    } catch (error) {
        console.error("Error fetching subcategories:", error);
        return [];
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ fetchAllSubCategories() {
    try {
        const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].subCategories);
        const data = await response.json();
        return data?.subcategories || [];
    } catch (error) {
        console.error("Error fetching subcategories:", error);
        return [];
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ fetchJobTitlesBySubCategory(subCategoryId) {
    try {
        const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].jobTitlesBySubCategory.replace(":sub_category_id", subCategoryId.toString()));
        const data = await response.json();
        return data?.job_titles || [];
    } catch (error) {
        console.error("Error fetching job titles:", error);
        return [];
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ deleteAttributeTitleById(attributeId) {
    try {
        const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].deleteAttribute.replace(":attribute_id", attributeId.toString()), {
            method: "DELETE"
        });
        return response.ok;
    } catch (error) {
        console.error("Error deleting job title:", error);
        return false;
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ fetchAttributeBySubcategoryId(subCategoryId) {
    try {
        const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].fetchAttributesBySubCategory.replace(":sub_category_id", subCategoryId.toString()));
        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error deleting job title:", error);
        return false;
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ updateAttributeWeight(subCategoryId, updatedData) {
    const url = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].updateAttributeWeight.replace(":sub_category_id", subCategoryId.toString());
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$post$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["postData"])(url, updatedData);
        return response; // Return the API response
    } catch (error) {
        console.error("Error updating attribute weight:", error);
        throw new Error("Failed to update attribute weight");
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ updateSubcategoryOfAttribute(attributeId, data) {
    const url = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].updateSubcategoryOfAttribute.replace(":attribute_id", attributeId.toString());
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$put$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["updateData"])(url, data);
        return response;
    } catch (error) {
        throw new Error("Failed to update attribute subcategory");
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ updateAttributeApprovalStatus(attributeId, data) {
    const url = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].updateAttributeApprovalStatus.replace(":attribute_id", attributeId.toString());
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$put$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["updateData"])(url, data);
        return response; // Return the API response
    } catch (error) {
        console.error("Error updating attribute approval status:", error);
        throw new Error("Failed to update attribute approval status");
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ fetchWeightsBySubcategoryId(subCategoryId) {
    try {
        const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].fetchWeightsBySubCategory.replace(":sub_category_id", subCategoryId.toString()));
        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error deleting job title:", error);
        return false;
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ fetchVacancies() {
    try {
        const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].getVacancies);
        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error deleting job title:", error);
        return false;
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ fetchVacanciesFromFiles() {
    try {
        const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].getVacanciesFromFiles);
        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error deleting job title:", error);
        return false;
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ fetchCandidatesByVacancyId(vacancyId) {
    try {
        const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].getCandidatesByVacancyId.replace(":vacancy_id", vacancyId.toString()));
        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error deleting job title:", error);
        return false;
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ fetchResumeByCandidateId(contactId) {
    try {
        const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].getResumeByContactId.replace(":contact_id", contactId.toString()));
        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error while fetching resume:", error);
        return false;
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ fetchResumeByCandidateIdFromFiles(contactId) {
    try {
        const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].getResumeFromFileByContactId.replace(":contact_id", contactId.toString()));
        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Error while fetching resume:", error);
        return false;
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ updateRecruiterReview(data) {
    const url = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].updateCandidatesReviewData;
    console.log("updateRecruiterReview data::", data);
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$post$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["postData"])(url, data);
        return response; // Return the API response
    } catch (error) {
        console.error("Error updating attribute weight:", error);
        throw new Error("Failed to update attribute weight");
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ getVacancyConfigAndStatus(vacancyId) {
    try {
        const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].getVacancyConfigAndStatus.replace(":vacancy_id", vacancyId.toString()));
        const data = await response.json();
        console.log("data::", data);
        return data;
    } catch (error) {
        console.error("Error while fetching vacancy status:", error);
        return false;
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ startVacancyreview(vacancyId, payload) {
    try {
        const url = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].startVacancyReview.replace(":vacancy_id", vacancyId.toString());
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$post$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["postData"])(url, payload);
        return response;
    } catch (error) {
        console.error("Error while start vacancy review:", error);
        return false;
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ completeVacancyreview(vacancyId, payload) {
    try {
        const url = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].completeVacancyReview.replace(":vacancy_id", vacancyId.toString());
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$post$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["postData"])(url, payload);
        return response;
    } catch (error) {
        console.error("Error while start vacancy review:", error);
        return false;
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ updateWhyFit(data) {
    try {
        const url = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].updateWhyFitData;
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$post$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["postData"])(url, data);
        return response;
    } catch (error) {
        console.log("Error while updating whyfit: ", error);
        return false;
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ fetchEntitlements(email_id, saveHistoryLogs) {
    try {
        const isEntitlementEnabled = process.env.IS_ENTITLEMENT_ENABLED === "true";
        if (!isEntitlementEnabled) {
            const response = {
                error: false,
                code: "TR_01",
                message: "Successful",
                entitlement: {
                    Work_force_Index: true,
                    Sub_Catregory: true,
                    Vacancy: true,
                    Search_Match: true,
                    Sc_Score_Config: true,
                    candidate_tunning_page: true,
                    Shorting_Listing: true,
                    Historical_Logs: true
                }
            };
            // Save entitlement in cookies
            const cookieStore = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])();
            cookieStore.set("entitlement", JSON.stringify(response.entitlement), {
                secure: true
            });
            return response;
        }
        const portal_name = "recruiter";
        const url = `${__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].getEntitlements}?email_id=${encodeURIComponent(email_id)}&portal_name=${encodeURIComponent(portal_name)}`;
        const response = await fetch(url);
        if (!response.ok) {
            throw new Error(`Failed to fetch entitlements: ${response.statusText}`);
        }
        const data = await response.json();
        console.log("Entitlement data:", data);
        if (saveHistoryLogs) {
            // Save entitlement in cookies
            (await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$headers$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["cookies"])()).set("entitlement", JSON.stringify(data.entitlement), {
                secure: true
            });
        }
        return data;
    } catch (error) {
        console.error("Error fetching entitlement data:", error);
        return false;
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ getAllSubcategoryWeightConfigs() {
    try {
        const response = await fetch(__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].getAllSubcategoryWeightConfigs);
        const data = await response.json();
        return data?.subcategory_weight_configs || [];
    } catch (error) {
        console.error("Error fetching subcategories:", error);
        return [];
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ updateSubcategoryWeightConfig(subcategoryId, data) {
    const url = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].updateSubcategoryWeightConfig.replace(":subcategory_id", subcategoryId.toString());
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$put$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["updateData"])(url, data);
        return response;
    } catch (error) {
        throw new Error("Failed to update SubcategoryWeightConfig");
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ postVacanciesShortlisted(data) {
    const url = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].vacanciesShortlisted;
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$post$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["postData"])(url, data);
        return response;
    } catch (error) {
        console.error("error::", error);
        throw error;
    }
}
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ postHistoricalLogs(email, portalName, featureName, metaData = null) {
    const url = __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].saveHistoryLogs.replace("{email_id}", email).replace("{portal_name}", portalName).replace("{feature}", featureName);
    try {
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$post$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["postData"])(url, metaData);
        if (!response || response.status >= 400) {
            console.error(`[postHistoricalLogs] Failed request: ${response.status} ${response.statusText}`);
            return null;
        }
        if (!response.data) {
            console.warn(`[postHistoricalLogs] Response missing data:`, response);
            throw new Error(`No data in response for historical logs.`);
        }
        return response.data;
    } catch (error) {
        console.error(`[postHistoricalLogs] Exception for ${email}:`, error);
        return null;
    }
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    fetchAllCategories,
    fetchAllSubCategories,
    fetchJobTitlesBySubCategory,
    deleteAttributeTitleById,
    fetchAttributeBySubcategoryId,
    updateAttributeWeight,
    updateSubcategoryOfAttribute,
    updateAttributeApprovalStatus,
    fetchWeightsBySubcategoryId,
    fetchVacancies,
    fetchVacanciesFromFiles,
    fetchCandidatesByVacancyId,
    fetchResumeByCandidateId,
    fetchResumeByCandidateIdFromFiles,
    updateRecruiterReview,
    getVacancyConfigAndStatus,
    startVacancyreview,
    completeVacancyreview,
    updateWhyFit,
    fetchEntitlements,
    getAllSubcategoryWeightConfigs,
    updateSubcategoryWeightConfig,
    postVacanciesShortlisted,
    postHistoricalLogs
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchAllCategories, "0070c516a719bc4d5da2991e171dfc1731b3bfcd01", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchAllSubCategories, "00dfd6ac6138fd6fdb4bb66cfe2e001b5b633b6248", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchJobTitlesBySubCategory, "40001ab8f91b17607478f15f3e331f94fa8f492027", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(deleteAttributeTitleById, "406ac29c78d366f5e75ca4601810ebec39513dc73f", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchAttributeBySubcategoryId, "40fd2068e785dd85c91ddd23fd7f0468fa988fe738", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(updateAttributeWeight, "6094ec7632250ae73ee9964addbc280e43b1d90039", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(updateSubcategoryOfAttribute, "60de454cb16c2ea8f0b7e2e25af8d0bfaa4de6e5bd", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(updateAttributeApprovalStatus, "60494406d8979ee9809192155ba859d5ec85a89895", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchWeightsBySubcategoryId, "4063428e91071df462c0b9076728a7d32615a943e7", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchVacancies, "00430c3fe55aae2c16d8c5809c583e907f184f0dc7", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchVacanciesFromFiles, "00eed182c174bc0ad16b9dd64ee0f54ce7fdab93b8", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchCandidatesByVacancyId, "40b49559358c35ff4b692eb8eb12ea9614a4e1f71f", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchResumeByCandidateId, "4058069912dbff67450c9f924a6c0aa1061b4c63d0", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchResumeByCandidateIdFromFiles, "402798ac62c44aa80cef8655242e6d58d9162b01ec", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(updateRecruiterReview, "406345071a46f57d39bc1d0cb464a8359afc40dca2", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(getVacancyConfigAndStatus, "40d478d951619bce460cfbc7d49a3cc493ba4b47ea", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(startVacancyreview, "60ff2f47ca078fbac86668827367cfc9554ca27bc5", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(completeVacancyreview, "6027034db188ca657eb89968e241062f20d7184639", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(updateWhyFit, "408d6350adccefe97f8e569bca319fd5565d4ba3ab", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(fetchEntitlements, "6053d6f48110c08a061373b14ac7e907994228ab59", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(getAllSubcategoryWeightConfigs, "0081f97a45fcc051f67f3f8979d95c4659f987c3e9", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(updateSubcategoryWeightConfig, "60177423cff0c80303d8847e8112cfa5122712a0f4", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(postVacanciesShortlisted, "406a3cc2b4fd2eb890eba0e64034b574ca766d629f", null);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(postHistoricalLogs, "7847e9e25707c39660f2fba6bfc09949a57236d8f3", null);
}}),
"[externals]/crypto [external] (crypto, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/querystring [external] (querystring, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("querystring", () => require("querystring"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/app/actions/getAppInsightsConnectionString.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
/* __next_internal_action_entry_do_not_use__ {"0019e5eeea75f1e298425b8560f1b6c359a66921b9":"getAppInsightsConnectionString"} */ __turbopack_esm__({
    "getAppInsightsConnectionString": (()=>getAppInsightsConnectionString)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/server-reference.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$app$2d$render$2f$encryption$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/server/app-render/encryption.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-validate.js [app-route] (ecmascript)");
;
;
async function /*#__TURBOPACK_DISABLE_EXPORT_MERGING__*/ getAppInsightsConnectionString() {
    return ("TURBOPACK compile-time value", "a2333a49-cb49-4a2c-8888-d5a4bc1ed97a;IngestionEndpoint=https://eastus-3.in.applicationinsights.azure.com/;LiveEndpoint=https://eastus.livediagnostics.monitor.azure.com/;ApplicationId=5edf80f7-54c1-42c5-910c-1c767571a849") || "";
}
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$action$2d$validate$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["ensureServerEntryExports"])([
    getAppInsightsConnectionString
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$webpack$2f$loaders$2f$next$2d$flight$2d$loader$2f$server$2d$reference$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["registerServerReference"])(getAppInsightsConnectionString, "0019e5eeea75f1e298425b8560f1b6c359a66921b9", null);
}}),
"[project]/library/appInsights.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "getAppInsights": (()=>getAppInsights),
    "initAppInsights": (()=>initAppInsights)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$getAppInsightsConnectionString$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/app/actions/getAppInsightsConnectionString.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$microsoft$2f$applicationinsights$2d$web$2f$dist$2d$es5$2f$AISku$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__AppInsightsSku__as__ApplicationInsights$3e$__ = __turbopack_import__("[project]/node_modules/@microsoft/applicationinsights-web/dist-es5/AISku.js [app-route] (ecmascript) <export AppInsightsSku as ApplicationInsights>");
;
;
let appInsights = null;
async function initAppInsights() {
    const appInsightsConnectionString = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$actions$2f$getAppInsightsConnectionString$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAppInsightsConnectionString"])();
    if (!appInsights) {
        const connectionString = "InstrumentationKey=" + appInsightsConnectionString || "";
        appInsights = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$microsoft$2f$applicationinsights$2d$web$2f$dist$2d$es5$2f$AISku$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$export__AppInsightsSku__as__ApplicationInsights$3e$__["ApplicationInsights"]({
            config: {
                connectionString,
                enableAutoRouteTracking: false
            }
        });
        appInsights.loadAppInsights();
    }
    return appInsights;
}
function getAppInsights() {
    return appInsights;
}
}}),
"[project]/library/utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "APPLICATION_NAVIGATION_ROUTES": (()=>APPLICATION_NAVIGATION_ROUTES),
    "FEATURE_NAMES": (()=>FEATURE_NAMES),
    "USER_UUID_KEY": (()=>USER_UUID_KEY),
    "clearUserUuid": (()=>clearUserUuid),
    "cn": (()=>cn),
    "emailInternalAddress": (()=>emailInternalAddress),
    "getOrCreateUserUuid": (()=>getOrCreateUserUuid)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/clsx/dist/clsx.mjs [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-route] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
const USER_UUID_KEY = "tg-user-uuid";
function getOrCreateUserUuid() {
    if ("TURBOPACK compile-time truthy", 1) return "";
    "TURBOPACK unreachable";
    let id;
}
function clearUserUuid() {
    localStorage.removeItem(USER_UUID_KEY); // call on logout if desired
}
var APPLICATION_NAVIGATION_ROUTES = /*#__PURE__*/ function(APPLICATION_NAVIGATION_ROUTES) {
    APPLICATION_NAVIGATION_ROUTES["VACANCY"] = "Vacancy";
    APPLICATION_NAVIGATION_ROUTES["WORK_FORCE_INDEX"] = "Work_force_Index";
    APPLICATION_NAVIGATION_ROUTES["SUB_CATEGORY"] = "Sub_Catregory";
    APPLICATION_NAVIGATION_ROUTES["SEARCH_MATCH"] = "Search_Match";
    APPLICATION_NAVIGATION_ROUTES["SC_SCORE_CONFIG"] = "Sc_Score_Config";
    return APPLICATION_NAVIGATION_ROUTES;
}({});
const emailInternalAddress = "<EMAIL>";
var FEATURE_NAMES = /*#__PURE__*/ function(FEATURE_NAMES) {
    FEATURE_NAMES["SORTLIST_CANDIDATE_FEATURE"] = "Shortlist Candidate Feature";
    FEATURE_NAMES["THUMB_REVIEW_FEATURE"] = "Thumb Review Feature";
    return FEATURE_NAMES;
}({});
}}),
"[project]/library/trackApi.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// lib/trackedFetch.ts
__turbopack_esm__({
    "trackedFetch": (()=>trackedFetch)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$library$2f$appInsights$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/library/appInsights.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$library$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/library/utils.ts [app-route] (ecmascript)");
;
;
async function trackedFetch(input, init = {}, extraCtx = {}) {
    const uuid = (0, __TURBOPACK__imported__module__$5b$project$5d2f$library$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getOrCreateUserUuid"])();
    // --- inject header so the backend can log/link this request too
    const headers = new Headers(init.headers);
    headers.set("X-User-UUID", uuid);
    const start = performance.now();
    try {
        const response = await fetch(input, {
            ...init,
            headers
        });
        const dur = performance.now() - start;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$library$2f$appInsights$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAppInsights"])()?.trackDependencyData({
            id: crypto.randomUUID(),
            name: typeof input === "string" ? input : input.toString(),
            target: window.location.hostname,
            duration: dur,
            success: response.ok,
            responseCode: response.status,
            type: "Fetch",
            properties: {
                userUuid: uuid,
                ...extraCtx
            }
        });
        if (!response.ok) throw new Error(`HTTP ${response.status}`);
        return response;
    } catch (err) {
        const dur = performance.now() - start;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$library$2f$appInsights$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAppInsights"])()?.trackDependencyData({
            id: crypto.randomUUID(),
            name: typeof input === "string" ? input : input.toString(),
            target: window.location.hostname,
            duration: dur,
            success: false,
            responseCode: 0,
            type: "Fetch",
            properties: {
                userUuid: uuid,
                error: err instanceof Error ? err.message : String(err),
                ...extraCtx
            }
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$library$2f$appInsights$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAppInsights"])()?.trackException({
            error: err,
            properties: {
                userUuid: uuid
            }
        });
        throw err;
    }
}
}}),
"[project]/library/auth.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "authOptions": (()=>authOptions)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/api/config.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$azure$2d$ad$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next-auth/providers/azure-ad.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$library$2f$trackApi$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/library/trackApi.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$library$2f$appInsights$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/library/appInsights.ts [app-route] (ecmascript)");
;
;
;
;
const authOptions = {
    providers: [
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$azure$2d$ad$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"])({
            clientId: process.env.RECRUITER_SSO_CLIENT_ID,
            clientSecret: process.env.RECRUITER_SSO_CLIENT_SECRET,
            tenantId: ("TURBOPACK compile-time value", "a197dec9-0864-4030-8028-86c9809afa83")
        })
    ],
    secret: process.env.NEXTAUTH_SECRET,
    pages: {
        signIn: `${process.env.NEXTAUTH_URL}/login`
    },
    callbacks: {
        async redirect ({ url, baseUrl }) {
            if (url.startsWith("/")) return `${baseUrl}${url}`;
            if (new URL(url).origin === baseUrl) return url;
            return baseUrl;
        },
        async session ({ session }) {
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
            const portal_name = "recruiter";
            let entitlement = {};
            try {
                // Caling an entitlements API to fetch user entitlements
                const url = `${__TURBOPACK__imported__module__$5b$project$5d2f$api$2f$config$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["API_ENDPOINTS"].getEntitlements}?email_id=${encodeURIComponent(session?.user?.email)}&portal_name=${encodeURIComponent(portal_name)}`;
                const res = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$library$2f$trackApi$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["trackedFetch"])(url, {}, {
                    context: "getEntitlements"
                });
                if (res.ok) {
                    const data = await res.json();
                    entitlement = data.entitlement;
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$library$2f$appInsights$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getAppInsights"])()?.trackEvent({
                        name: "FE_Entitlements_Fetched",
                        properties: {
                            email: session?.user?.email
                        }
                    });
                } else {
                    console.error("Failed to fetch entitlements:", res.statusText);
                }
            } catch (err) {
                console.error("Error fetching entitlements:", err);
            }
            return {
                ...session,
                entitlement
            };
        }
    }
};
}}),
"[project]/utils/auth-utils.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "AuthErrorHandler": (()=>AuthErrorHandler),
    "CheckAuth": (()=>CheckAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next-auth/index.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$library$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/library/auth.ts [app-route] (ecmascript)");
;
;
const adAuthorization = ("TURBOPACK compile-time value", "true") === "true";
async function CheckAuth(route) {
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["getServerSession"])(__TURBOPACK__imported__module__$5b$project$5d2f$library$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["authOptions"]);
    const entitlement = session?.entitlement || {};
    const isAuthorized = entitlement[route || ""] || undefined;
    if ("TURBOPACK compile-time truthy", 1) {
        if (session && isAuthorized) {
            return true;
        } else {
            return false;
        }
    } else {
        "TURBOPACK unreachable";
    }
}
async function AuthErrorHandler() {
    return new Response(JSON.stringify({
        message: "Unauthenticated Access, please login to the portal"
    }), {
        status: 403
    });
}
}}),
"[project]/app/api/subcategories/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$serverActions$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/api/serverActions.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/utils/auth-utils.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$library$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/library/utils.ts [app-route] (ecmascript)");
;
;
;
async function GET() {
    const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["CheckAuth"])(__TURBOPACK__imported__module__$5b$project$5d2f$library$2f$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["APPLICATION_NAVIGATION_ROUTES"].SUB_CATEGORY);
    if (response) {
        const subCategories = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$api$2f$serverActions$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["fetchAllSubCategories"])();
        return Response.json(subCategories);
    } else {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$utils$2f$auth$2d$utils$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["AuthErrorHandler"])();
    }
}
}}),
"[project]/ (server-utils)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__4d1006._.js.map