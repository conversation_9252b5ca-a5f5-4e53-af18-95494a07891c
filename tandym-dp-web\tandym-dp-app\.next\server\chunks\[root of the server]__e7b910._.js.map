{"version": 3, "sources": [], "sections": [{"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/post.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nexport const postData = async (url: string, data: any) => {\r\n  try {\r\n    const response = await axios(url, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      data,\r\n    });\r\n    return response;\r\n  } catch (error: any) {\r\n    let message =\r\n      error.response?.data?.detail?.message ||\r\n      error.response?.data?.error ||\r\n      error.message ||\r\n      \"Unknown error\";\r\n    const err = new Error(message);\r\n    err.name = \"ApiError\";\r\n    // Attach extra info for advanced error handling\r\n    (err as any).status = error.response?.status || 500;\r\n    (err as any).data = error.response?.data ?? null;\r\n    throw err;\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,WAAW,OAAO,KAAa;IAC1C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAK,AAAD,EAAE,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,IAAI,UACF,MAAM,QAAQ,EAAE,MAAM,QAAQ,WAC9B,MAAM,QAAQ,EAAE,MAAM,SACtB,MAAM,OAAO,IACb;QACF,MAAM,MAAM,IAAI,MAAM;QACtB,IAAI,IAAI,GAAG;QACX,gDAAgD;QAC/C,IAAY,MAAM,GAAG,MAAM,QAAQ,EAAE,UAAU;QAC/C,IAAY,IAAI,GAAG,MAAM,QAAQ,EAAE,QAAQ;QAC5C,MAAM;IACR;AACF"}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/put.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nexport const updateData = async (url: string, data: any) => {\r\n  try {\r\n    const response = await axios(url, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      data,\r\n    });\r\n    return response;\r\n  } catch (error: any) {\r\n    throw new Error(error.message);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,aAAa,OAAO,KAAa;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAK,AAAD,EAAE,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,OAAO;IAC/B;AACF"}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 208, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/app/api/vacancies/resume/%5Bid%5D/route.ts"], "sourcesContent": ["// app/api/jobs/[id]/route.ts\r\nimport { fetchResumeByCandidateId } from \"@/api/serverActions\";\r\nimport { Auth<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CheckA<PERSON> } from \"@/utils/auth-utils\";\r\nimport { APPLICATION_NAVIGATION_ROUTES } from \"@/library/utils\";\r\nimport { NextRequest } from \"next/server\";\r\n\r\nexport async function GET(\r\n  req: NextRequest,\r\n  { params }: { params: Promise<{ id: string }> }\r\n) {\r\n  const contactId = (await params).id;\r\n  const candidateResume = await fetchResumeByCandidateId(contactId);\r\n  return Response.json(candidateResume);\r\n}\r\n"], "names": [], "mappings": "AAAA,6BAA6B;;;;AAC7B;;AAKO,eAAe,IACpB,GAAgB,EAChB,EAAE,MAAM,EAAuC;IAE/C,MAAM,YAAY,CAAC,MAAM,MAAM,EAAE,EAAE;IACnC,MAAM,kBAAkB,MAAM,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD,EAAE;IACvD,OAAO,SAAS,IAAI,CAAC;AACvB"}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}