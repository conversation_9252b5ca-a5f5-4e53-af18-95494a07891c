{"version": 3, "sources": [], "sections": [{"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/config.ts"], "sourcesContent": ["const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || \"http://0.0.0.0:8005\";\r\nconst PORTAL_SERVICE_BASE_URL =\r\n  process.env.DP_PORTAL_SERVICE || \"http://0.0.0.0:8006\";\r\n// Ensure this is the correct base URL for your experiment APIs,\r\n// the example uses localhost:8006, so adjust if necessary.\r\n// For this example, I will use the user-provided localhost:8006\r\nexport const EXPERIMENT_BASE_URL =\r\n  process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:8005\";\r\n\r\nexport const isADLogin = (): boolean =>\r\n  process.env.NEXT_PUBLIC_AD_LOGIN === \"true\";\r\n\r\nexport const isParsedResume =\r\n  process.env.NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY === \"true\";\r\n\r\nexport const IS_WHY_FIT_EDITABLE = process.env.NEXT_PUBLIC_IS_WHY_FIT_EDITABLE;\r\n\r\nexport const IS_LOCK_FEATURE_DISABLED =\r\n  process.env.NEXT_PUBLIC_IS_LOCK_FEATURE_DISABLED;\r\n\r\nexport const API_ENDPOINTS = {\r\n  categories: `${BASE_URL}/categories`,\r\n  subCategories: `${BASE_URL}/subcategories`,\r\n  subCategoriesPools: `${BASE_URL}/subcategory/pools`,\r\n  jobTitlesBySubCategory: `${BASE_URL}/jobtitles/:sub_category_id?limit=10000000`,\r\n  deleteAttribute: `${BASE_URL}/attribute/delete/:attribute_id`,\r\n  fetchAttributesBySubCategory: `${BASE_URL}/attributes/:sub_category_id?limit=10000000`,\r\n  fetchWeightsBySubCategory: `${BASE_URL}/weights/:sub_category_id`,\r\n  updateAttributeWeight: `${BASE_URL}/attributes/:sub_category_id/update`,\r\n  updateSubcategoryOfAttribute: `${BASE_URL}/attributes/:attribute_id/subcategory`,\r\n  updateAttributeApprovalStatus: `${BASE_URL}/attributes/:attribute_id/approval`,\r\n  candidatesData: `${BASE_URL}/candidates`,\r\n  updateCandidatesReviewData: `${BASE_URL}/candidates/update_in_db`,\r\n  jobsData: `${BASE_URL}/jobs`,\r\n  getVacancies: `${BASE_URL}/vacancies`,\r\n  getVacanciesFromFiles: `${BASE_URL}/files/vacancies`,\r\n  getCandidatesByVacancyId: `${BASE_URL}/candidates/:vacancy_id`,\r\n  getResumeByContactId: `${BASE_URL}/resume/:contact_id`,\r\n  getResumeFromFileByContactId: `${BASE_URL}/files/candidate-resume/:contact_id`,\r\n  getAllSubcategoryWeightConfigs: `${BASE_URL}/v1/subcategory/weight-configs`,\r\n  updateSubcategoryWeightConfig: `${BASE_URL}/v1/subcategory/weight-configs/:subcategory_id`,\r\n  getCandidateStats: `${BASE_URL}/api/candidate-stats`,\r\n  getEntitlements: `${PORTAL_SERVICE_BASE_URL}/api/entitlement`,\r\n  getVacancyConfigAndStatus: `${BASE_URL}/vacancies/:vacancy_id/status`,\r\n  startVacancyReview: `${BASE_URL}/vacancies/:vacancy_id/start`,\r\n  completeVacancyReview: `${BASE_URL}/vacancies/:vacancy_id/complete`,\r\n  updateWhyFitData: `${BASE_URL}/candidates/fitness_reason`,\r\n\r\n  // New Experiment Endpoints\r\n  experimentGetVacancies: `${EXPERIMENT_BASE_URL}/experiment/vacancies`,\r\n  experimentGetVacancyRunDetails: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/details`,\r\n  experimentGetCandidatesForVacancyRun: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/candidates`,\r\n  experimentGetRunConfig: `${EXPERIMENT_BASE_URL}/experiment/runs/:run_id/config`,\r\n  experimentGetCandidateResume: `${EXPERIMENT_BASE_URL}/experiment/candidates/:contact_id/resume`,\r\n  experimentArchiveVacancy: `${EXPERIMENT_BASE_URL}/experiment/vacancies/archive`, // Added new endpoint\r\n  experimentPromoteResults: `${EXPERIMENT_BASE_URL}/experiment/results/promote`, // + New endpoint for promoting results\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAA,MAAM,WAAW,2DAAoC;AACrD,MAAM,0BACJ,QAAQ,GAAG,CAAC,iBAAiB,IAAI;AAI5B,MAAM,sBACX,2DAAoC;AAE/B,MAAM,YAAY,IACvB,6CAAqC;AAEhC,MAAM,iBACX,6CAAsD;AAEjD,MAAM;AAEN,MAAM;AAGN,MAAM,gBAAgB;IAC3B,YAAY,GAAG,SAAS,WAAW,CAAC;IACpC,eAAe,GAAG,SAAS,cAAc,CAAC;IAC1C,oBAAoB,GAAG,SAAS,kBAAkB,CAAC;IACnD,wBAAwB,GAAG,SAAS,0CAA0C,CAAC;IAC/E,iBAAiB,GAAG,SAAS,+BAA+B,CAAC;IAC7D,8BAA8B,GAAG,SAAS,2CAA2C,CAAC;IACtF,2BAA2B,GAAG,SAAS,yBAAyB,CAAC;IACjE,uBAAuB,GAAG,SAAS,mCAAmC,CAAC;IACvE,8BAA8B,GAAG,SAAS,qCAAqC,CAAC;IAChF,+BAA+B,GAAG,SAAS,kCAAkC,CAAC;IAC9E,gBAAgB,GAAG,SAAS,WAAW,CAAC;IACxC,4BAA4B,GAAG,SAAS,wBAAwB,CAAC;IACjE,UAAU,GAAG,SAAS,KAAK,CAAC;IAC5B,cAAc,GAAG,SAAS,UAAU,CAAC;IACrC,uBAAuB,GAAG,SAAS,gBAAgB,CAAC;IACpD,0BAA0B,GAAG,SAAS,uBAAuB,CAAC;IAC9D,sBAAsB,GAAG,SAAS,mBAAmB,CAAC;IACtD,8BAA8B,GAAG,SAAS,mCAAmC,CAAC;IAC9E,gCAAgC,GAAG,SAAS,8BAA8B,CAAC;IAC3E,+BAA+B,GAAG,SAAS,8CAA8C,CAAC;IAC1F,mBAAmB,GAAG,SAAS,oBAAoB,CAAC;IACpD,iBAAiB,GAAG,wBAAwB,gBAAgB,CAAC;IAC7D,2BAA2B,GAAG,SAAS,6BAA6B,CAAC;IACrE,oBAAoB,GAAG,SAAS,4BAA4B,CAAC;IAC7D,uBAAuB,GAAG,SAAS,+BAA+B,CAAC;IACnE,kBAAkB,GAAG,SAAS,0BAA0B,CAAC;IAEzD,2BAA2B;IAC3B,wBAAwB,GAAG,oBAAoB,qBAAqB,CAAC;IACrE,gCAAgC,GAAG,oBAAoB,sDAAsD,CAAC;IAC9G,sCAAsC,GAAG,oBAAoB,yDAAyD,CAAC;IACvH,wBAAwB,GAAG,oBAAoB,+BAA+B,CAAC;IAC/E,8BAA8B,GAAG,oBAAoB,yCAAyC,CAAC;IAC/F,0BAA0B,GAAG,oBAAoB,6BAA6B,CAAC;IAC/E,0BAA0B,GAAG,oBAAoB,2BAA2B,CAAC;AAC/E"}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/post.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nexport const postData = async (url: string, data: any) => {\r\n  try {\r\n    const response = await axios(url, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      data,\r\n    });\r\n    return response;\r\n  } catch (error: any) {\r\n    throw new Error(error.message);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,WAAW,OAAO,KAAa;IAC1C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAK,AAAD,EAAE,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,OAAO;IAC/B;AACF"}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 225, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/put.ts"], "sourcesContent": ["import axios from \"axios\";\r\n\r\nexport const updateData = async (url: string, data: any) => {\r\n  try {\r\n    const response = await axios(url, {\r\n      method: \"PUT\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      data,\r\n    });\r\n    return response;\r\n  } catch (error: any) {\r\n    throw new Error(error.message);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,aAAa,OAAO,KAAa;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,uIAAA,CAAA,UAAK,AAAD,EAAE,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA;QACF;QACA,OAAO;IACT,EAAE,OAAO,OAAY;QACnB,MAAM,IAAI,MAAM,MAAM,OAAO;IAC/B;AACF"}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/serverActions.ts"], "sourcesContent": ["// api/serverActions.ts\r\n\"use server\";\r\nimport { API_ENDPOINTS } from \"./config\";\r\nimport { postData } from \"./post\";\r\nimport { updateData } from \"./put\";\r\nimport { EntitlementResponse } from \"./types\";\r\n\r\nexport async function fetchAllCategories() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.categories);\r\n    const data = await response.json();\r\n    return data?.categories || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching subcategories:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\nexport async function fetchAllSubCategories() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.subCategories);\r\n    const data = await response.json();\r\n    return data?.subcategories || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching subcategories:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\nexport async function fetchJobTitlesBySubCategory(subCategoryId: number) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.jobTitlesBySubCategory.replace(\r\n        \":sub_category_id\",\r\n        subCategoryId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data?.job_titles || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching job titles:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\nexport async function deleteAttributeTitleById(attributeId: number) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.deleteAttribute.replace(\r\n        \":attribute_id\",\r\n        attributeId.toString()\r\n      ),\r\n      { method: \"DELETE\" }\r\n    );\r\n    return response.ok;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchAttributeBySubcategoryId(subCategoryId: number) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.fetchAttributesBySubCategory.replace(\r\n        \":sub_category_id\",\r\n        subCategoryId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function updateAttributeWeight(\r\n  subCategoryId: number,\r\n  updatedData: any\r\n) {\r\n  const url = API_ENDPOINTS.updateAttributeWeight.replace(\r\n    \":sub_category_id\",\r\n    subCategoryId.toString()\r\n  );\r\n\r\n  try {\r\n    const response = await postData(url, updatedData);\r\n    return response; // Return the API response\r\n  } catch (error) {\r\n    console.error(\"Error updating attribute weight:\", error);\r\n    throw new Error(\"Failed to update attribute weight\");\r\n  }\r\n}\r\n\r\nexport async function updateSubcategoryOfAttribute(\r\n  attributeId: number,\r\n  data: { new_subcategory_id: number }\r\n) {\r\n  const url = API_ENDPOINTS.updateSubcategoryOfAttribute.replace(\r\n    \":attribute_id\",\r\n    attributeId.toString()\r\n  );\r\n\r\n  try {\r\n    const response = await updateData(url, data);\r\n    return response;\r\n  } catch (error) {\r\n    throw new Error(\"Failed to update attribute subcategory\");\r\n  }\r\n}\r\n\r\nexport async function updateAttributeApprovalStatus(\r\n  attributeId: number,\r\n  data: { is_approved: boolean }\r\n) {\r\n  const url = API_ENDPOINTS.updateAttributeApprovalStatus.replace(\r\n    \":attribute_id\",\r\n    attributeId.toString()\r\n  );\r\n\r\n  try {\r\n    const response = await updateData(url, data);\r\n    return response; // Return the API response\r\n  } catch (error) {\r\n    console.error(\"Error updating attribute approval status:\", error);\r\n    throw new Error(\"Failed to update attribute approval status\");\r\n  }\r\n}\r\n\r\nexport async function fetchWeightsBySubcategoryId(subCategoryId: number) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.fetchWeightsBySubCategory.replace(\r\n        \":sub_category_id\",\r\n        subCategoryId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchVacancies() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.getVacancies);\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchVacanciesFromFiles() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.getVacanciesFromFiles);\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchCandidatesByVacancyId(vacancyId: string) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.getCandidatesByVacancyId.replace(\r\n        \":vacancy_id\",\r\n        vacancyId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error deleting job title:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchResumeByCandidateId(contactId: string) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.getResumeByContactId.replace(\r\n        \":contact_id\",\r\n        contactId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error while fetching resume:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function fetchResumeByCandidateIdFromFiles(contactId: string) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.getResumeFromFileByContactId.replace(\r\n        \":contact_id\",\r\n        contactId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error while fetching resume:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\ninterface RecruiterPostReview {\r\n  hiring_decision: string;\r\n  review_message: string;\r\n  candidate_contact_id: string;\r\n  recruiter_email: string;\r\n}\r\n\r\nexport async function updateRecruiterReview(data: any) {\r\n  const url = API_ENDPOINTS.updateCandidatesReviewData;\r\n  console.log(\"updateRecruiterReview data::\", data);\r\n  try {\r\n    const response = await postData(url, data);\r\n    return response; // Return the API response\r\n  } catch (error) {\r\n    console.error(\"Error updating attribute weight:\", error);\r\n    throw new Error(\"Failed to update attribute weight\");\r\n  }\r\n}\r\n\r\nexport async function getVacancyConfigAndStatus(vacancyId: string) {\r\n  try {\r\n    const response = await fetch(\r\n      API_ENDPOINTS.getVacancyConfigAndStatus.replace(\r\n        \":vacancy_id\",\r\n        vacancyId.toString()\r\n      )\r\n    );\r\n    const data = await response.json();\r\n    console.log(\"data::\", data);\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error while fetching vacancy status:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function startVacancyreview(\r\n  vacancyId: string,\r\n  payload: { reviewer: string }\r\n) {\r\n  try {\r\n    const url = API_ENDPOINTS.startVacancyReview.replace(\r\n      \":vacancy_id\",\r\n      vacancyId.toString()\r\n    );\r\n    const response = await postData(url, payload);\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error while start vacancy review:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function completeVacancyreview(\r\n  vacancyId: string,\r\n  payload: { reviewer: string }\r\n) {\r\n  try {\r\n    const url = API_ENDPOINTS.completeVacancyReview.replace(\r\n      \":vacancy_id\",\r\n      vacancyId.toString()\r\n    );\r\n    const response = await postData(url, payload);\r\n    return response;\r\n  } catch (error) {\r\n    console.error(\"Error while start vacancy review:\", error);\r\n    return false;\r\n  }\r\n}\r\n\r\nexport async function updateWhyFit(data: any) {\r\n  try {\r\n    const url = API_ENDPOINTS.updateWhyFitData;\r\n    const response = await postData(url, data);\r\n    return response;\r\n  } catch (error) {\r\n    console.log(\"Error while updating whyfit: \", error);\r\n    return false;\r\n  }\r\n}\r\nexport async function fetchEntitlements(\r\n  email_id: string\r\n): Promise<EntitlementResponse | false> {\r\n  try {\r\n    const isEntitlementEnabled = process.env.IS_ENTITLEMENT_ENABLED === \"true\";\r\n    if (!isEntitlementEnabled) {\r\n      return {\r\n        error: false,\r\n        code: \"TR_01\",\r\n        message: \"Successful\",\r\n        entitlement: {\r\n          Work_force_Index: true,\r\n          Sub_Catregory: true,\r\n          Vacancy: true,\r\n          Search_Match: true,\r\n          Sc_Score_Config: true,\r\n        },\r\n      };\r\n    }\r\n    const portal_name = \"recruiter\";\r\n    const url = `${API_ENDPOINTS.getEntitlements}?email_id=${encodeURIComponent(\r\n      email_id\r\n    )}&portal_name=${encodeURIComponent(portal_name)}`;\r\n    const response = await fetch(url);\r\n    if (!response.ok) {\r\n      throw new Error(`Failed to fetch entitlements: ${response.statusText}`);\r\n    }\r\n\r\n    const data: EntitlementResponse = await response.json();\r\n    return data;\r\n  } catch (error) {\r\n    console.error(\"Error fetching entitlement data:\", error);\r\n    return false;\r\n  }\r\n}\r\nexport async function getAllSubcategoryWeightConfigs() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.getAllSubcategoryWeightConfigs);\r\n    const data = await response.json();\r\n    return data?.subcategory_weight_configs || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching subcategories:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\nexport async function updateSubcategoryWeightConfig(\r\n  subcategoryId: number,\r\n  data: any\r\n) {\r\n  const url = API_ENDPOINTS.updateSubcategoryWeightConfig.replace(\r\n    \":subcategory_id\",\r\n    subcategoryId.toString()\r\n  );\r\n\r\n  try {\r\n    const response = await updateData(url, data);\r\n    return response;\r\n  } catch (error) {\r\n    throw new Error(\"Failed to update SubcategoryWeightConfig\");\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvB;AACA;AACA;;;;;;;AAGO,eAAe,uCAAgB,GAAhB;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,UAAU;QACrD,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,MAAM,cAAc,EAAE;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,EAAE;IACX;AACF;AAEO,eAAe,uCAAmB,GAAnB;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,aAAa;QACxD,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,MAAM,iBAAiB,EAAE;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,EAAE;IACX;AACF;AAEO,eAAe,uCAAyB,GAAzB,4BAA4B,aAAqB;IACrE,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,sBAAsB,CAAC,OAAO,CAC1C,oBACA,cAAc,QAAQ;QAG1B,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,MAAM,cAAc,EAAE;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,EAAE;IACX;AACF;AAEO,eAAe,uCAAsB,GAAtB,yBAAyB,WAAmB;IAChE,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,eAAe,CAAC,OAAO,CACnC,iBACA,YAAY,QAAQ,KAEtB;YAAE,QAAQ;QAAS;QAErB,OAAO,SAAS,EAAE;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAA2B,GAA3B,8BAA8B,aAAqB;IACvE,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,4BAA4B,CAAC,OAAO,CAChD,oBACA,cAAc,QAAQ;QAG1B,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAAmB,GAAnB,sBACpB,aAAqB,EACrB,WAAgB;IAEhB,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,qBAAqB,CAAC,OAAO,CACrD,oBACA,cAAc,QAAQ;IAGxB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACrC,OAAO,UAAU,0BAA0B;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,uCAA0B,GAA1B,6BACpB,WAAmB,EACnB,IAAoC;IAEpC,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,4BAA4B,CAAC,OAAO,CAC5D,iBACA,YAAY,QAAQ;IAGtB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,4GAAA,CAAA,aAAU,AAAD,EAAE,KAAK;QACvC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,uCAA2B,GAA3B,8BACpB,WAAmB,EACnB,IAA8B;IAE9B,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,6BAA6B,CAAC,OAAO,CAC7D,iBACA,YAAY,QAAQ;IAGtB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,4GAAA,CAAA,aAAU,AAAD,EAAE,KAAK;QACvC,OAAO,UAAU,0BAA0B;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,uCAAyB,GAAzB,4BAA4B,aAAqB;IACrE,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,yBAAyB,CAAC,OAAO,CAC7C,oBACA,cAAc,QAAQ;QAG1B,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAAY,GAAZ;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,YAAY;QACvD,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAAqB,GAArB;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,qBAAqB;QAChE,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAAwB,GAAxB,2BAA2B,SAAiB;IAChE,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,wBAAwB,CAAC,OAAO,CAC5C,eACA,UAAU,QAAQ;QAGtB,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,OAAO;IACT;AACF;AAEO,eAAe,uCAAsB,GAAtB,yBAAyB,SAAiB;IAC9D,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,oBAAoB,CAAC,OAAO,CACxC,eACA,UAAU,QAAQ;QAGtB,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AAEO,eAAe,uCAA+B,GAA/B,kCAAkC,SAAiB;IACvE,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,4BAA4B,CAAC,OAAO,CAChD,eACA,UAAU,QAAQ;QAGtB,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;IACT;AACF;AASO,eAAe,uCAAmB,GAAnB,sBAAsB,IAAS;IACnD,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,0BAA0B;IACpD,QAAQ,GAAG,CAAC,gCAAgC;IAC5C,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACrC,OAAO,UAAU,0BAA0B;IAC7C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,MAAM,IAAI,MAAM;IAClB;AACF;AAEO,eAAe,uCAAuB,GAAvB,0BAA0B,SAAiB;IAC/D,IAAI;QACF,MAAM,WAAW,MAAM,MACrB,+GAAA,CAAA,gBAAa,CAAC,yBAAyB,CAAC,OAAO,CAC7C,eACA,UAAU,QAAQ;QAGtB,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,UAAU;QACtB,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wCAAwC;QACtD,OAAO;IACT;AACF;AAEO,eAAe,uCAAgB,GAAhB,mBACpB,SAAiB,EACjB,OAA6B;IAE7B,IAAI;QACF,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,kBAAkB,CAAC,OAAO,CAClD,eACA,UAAU,QAAQ;QAEpB,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACrC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAEO,eAAe,uCAAmB,GAAnB,sBACpB,SAAiB,EACjB,OAA6B;IAE7B,IAAI;QACF,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,qBAAqB,CAAC,OAAO,CACrD,eACA,UAAU,QAAQ;QAEpB,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACrC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,OAAO;IACT;AACF;AAEO,eAAe,uCAAU,GAAV,aAAa,IAAS;IAC1C,IAAI;QACF,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,gBAAgB;QAC1C,MAAM,WAAW,MAAM,CAAA,GAAA,6GAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACrC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,GAAG,CAAC,iCAAiC;QAC7C,OAAO;IACT;AACF;AACO,eAAe,uCAAe,GAAf,kBACpB,QAAgB;IAEhB,IAAI;QACF,MAAM,uBAAuB,QAAQ,GAAG,CAAC,sBAAsB,KAAK;QACpE,IAAI,CAAC,sBAAsB;YACzB,OAAO;gBACL,OAAO;gBACP,MAAM;gBACN,SAAS;gBACT,aAAa;oBACX,kBAAkB;oBAClB,eAAe;oBACf,SAAS;oBACT,cAAc;oBACd,iBAAiB;gBACnB;YACF;QACF;QACA,MAAM,cAAc;QACpB,MAAM,MAAM,GAAG,+GAAA,CAAA,gBAAa,CAAC,eAAe,CAAC,UAAU,EAAE,mBACvD,UACA,aAAa,EAAE,mBAAmB,cAAc;QAClD,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,SAAS,UAAU,EAAE;QACxE;QAEA,MAAM,OAA4B,MAAM,SAAS,IAAI;QACrD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;IACT;AACF;AACO,eAAe,uCAA4B,GAA5B;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,8BAA8B;QACzE,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,MAAM,8BAA8B,EAAE;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,EAAE;IACX;AACF;AAEO,eAAe,uCAA2B,GAA3B,8BACpB,aAAqB,EACrB,IAAS;IAET,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,6BAA6B,CAAC,OAAO,CAC7D,mBACA,cAAc,QAAQ;IAGxB,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,4GAAA,CAAA,aAAU,AAAD,EAAE,KAAK;QACvC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,IAAI,MAAM;IAClB;AACF;;;IA9VsB;IAWA;IAWA;IAgBA;IAgBA;IAgBA;IAkBA;IAiBA;IAkBA;IAgBA;IAWA;IAWA;IAgBA;IAgBA;IAuBA;IAYA;IAiBA;IAiBA;IAiBA;IAUA;IAmCA;IAWA;;AA/UA,iPAAA;AAWA,iPAAA;AAWA,iPAAA;AAgBA,iPAAA;AAgBA,iPAAA;AAgBA,iPAAA;AAkBA,iPAAA;AAiBA,iPAAA;AAkBA,iPAAA;AAgBA,iPAAA;AAWA,iPAAA;AAWA,iPAAA;AAgBA,iPAAA;AAgBA,iPAAA;AAuBA,iPAAA;AAYA,iPAAA;AAiBA,iPAAA;AAiBA,iPAAA;AAiBA,iPAAA;AAUA,iPAAA;AAmCA,iPAAA;AAWA,iPAAA"}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/app/actions/getAppInsightsConnectionString.ts"], "sourcesContent": ["\"use server\";\r\n\r\nexport async function getAppInsightsConnectionString() {\r\n  return process.env.NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING || \"\";\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAEO,eAAe,uCAA4B,GAA5B;IACpB,OAAO,mQAAyD;AAClE;;;IAFsB;;AAAA,iPAAA"}}, {"offset": {"line": 628, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/appInsights.ts"], "sourcesContent": ["import { getAppInsightsConnectionString } from \"@/app/actions/getAppInsightsConnectionString\";\r\nimport { ApplicationInsights } from \"@microsoft/applicationinsights-web\";\r\n\r\nlet appInsights: ApplicationInsights | null = null;\r\n\r\nexport async function initAppInsights() {\r\n  const appInsightsConnectionString = await getAppInsightsConnectionString();\r\n\r\n  if (!appInsights) {\r\n    const connectionString =\r\n      \"InstrumentationKey=\" + appInsightsConnectionString || \"\";\r\n    appInsights = new ApplicationInsights({\r\n      config: {\r\n        connectionString,\r\n        enableAutoRouteTracking: false, // We'll do this manually\r\n      },\r\n    });\r\n    appInsights.loadAppInsights();\r\n  }\r\n  return appInsights;\r\n}\r\n\r\nexport function getAppInsights() {\r\n  return appInsights;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,IAAI,cAA0C;AAEvC,eAAe;IACpB,MAAM,8BAA8B,MAAM,CAAA,GAAA,kJAAA,CAAA,iCAA8B,AAAD;IAEvE,IAAI,CAAC,aAAa;QAChB,MAAM,mBACJ,wBAAwB,+BAA+B;QACzD,cAAc,IAAI,4OAAA,CAAA,sBAAmB,CAAC;YACpC,QAAQ;gBACN;gBACA,yBAAyB;YAC3B;QACF;QACA,YAAY,eAAe;IAC7B;IACA,OAAO;AACT;AAEO,SAAS;IACd,OAAO;AACT"}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const USER_UUID_KEY = \"tg-user-uuid\";\r\n\r\nexport function getOrCreateUserUuid(): string {\r\n  if (typeof window === \"undefined\") return \"\";\r\n  let id = localStorage.getItem(USER_UUID_KEY);\r\n  if (!id) {\r\n    id = crypto.randomUUID();\r\n    localStorage.setItem(USER_UUID_KEY, id);\r\n  }\r\n  return id;\r\n}\r\n\r\nexport function clearUserUuid() {\r\n  localStorage.removeItem(USER_UUID_KEY); // call on logout if desired\r\n}\r\nexport enum APPLICATION_NAVIGATION_ROUTES {\r\n  VACANCY = \"Vacancy\",\r\n  WORK_FORCE_INDEX = \"Work_force_Index\",\r\n  SUB_CATEGORY = \"Sub_Catregory\",\r\n  SEARCH_MATCH = \"Search_Match\",\r\n  SC_SCORE_CONFIG = \"Sc_Score_Config\",\r\n}\r\nexport const emailInternalAddress = \"<EMAIL>\";\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,gBAAgB;AAEtB,SAAS;IACd,wCAAmC,OAAO;;IAC1C,IAAI;AAMN;AAEO,SAAS;IACd,aAAa,UAAU,CAAC,gBAAgB,4BAA4B;AACtE;AACO,IAAA,AAAK,uDAAA;;;;;;WAAA;;AAOL,MAAM,uBAAuB"}}, {"offset": {"line": 699, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 705, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/trackApi.ts"], "sourcesContent": ["// lib/trackedFetch.ts\r\nimport { getAppInsights } from \"./appInsights\";\r\nimport { getOrCreateUserUuid } from \"./utils\";\r\n\r\nexport async function trackedFetch(\r\n  input: RequestInfo | URL,\r\n  init: RequestInit = {},\r\n  extraCtx: Record<string, any> = {}\r\n): Promise<Response> {\r\n  const uuid = getOrCreateUserUuid();\r\n\r\n  // --- inject header so the backend can log/link this request too\r\n  const headers = new Headers(init.headers);\r\n  headers.set(\"X-User-UUID\", uuid);\r\n\r\n  const start = performance.now();\r\n  try {\r\n    const response = await fetch(input, { ...init, headers });\r\n    const dur = performance.now() - start;\r\n\r\n    getAppInsights()?.trackDependencyData({\r\n      id: crypto.randomUUID(), // per-call correlation id\r\n      name: typeof input === \"string\" ? input : input.toString(),\r\n      target: window.location.hostname,\r\n      duration: dur,\r\n      success: response.ok,\r\n      responseCode: response.status,\r\n      type: \"Fetch\",\r\n      properties: { userUuid: uuid, ...extraCtx },\r\n    });\r\n\r\n    if (!response.ok) throw new Error(`HTTP ${response.status}`);\r\n    return response;\r\n  } catch (err) {\r\n    const dur = performance.now() - start;\r\n    getAppInsights()?.trackDependencyData({\r\n      id: crypto.randomUUID(),\r\n      name: typeof input === \"string\" ? input : input.toString(),\r\n      target: window.location.hostname,\r\n      duration: dur,\r\n      success: false,\r\n      responseCode: 0,\r\n      type: \"Fetch\",\r\n      properties: {\r\n        userUuid: uuid,\r\n        error: err instanceof Error ? err.message : String(err),\r\n        ...extraCtx,\r\n      },\r\n    });\r\n    getAppInsights()?.trackException({\r\n      error: err as Error,\r\n      properties: { userUuid: uuid },\r\n    });\r\n    throw err;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AACtB;AACA;;;AAEO,eAAe,aACpB,KAAwB,EACxB,OAAoB,CAAC,CAAC,EACtB,WAAgC,CAAC,CAAC;IAElC,MAAM,OAAO,CAAA,GAAA,kHAAA,CAAA,sBAAmB,AAAD;IAE/B,iEAAiE;IACjE,MAAM,UAAU,IAAI,QAAQ,KAAK,OAAO;IACxC,QAAQ,GAAG,CAAC,eAAe;IAE3B,MAAM,QAAQ,YAAY,GAAG;IAC7B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,OAAO;YAAE,GAAG,IAAI;YAAE;QAAQ;QACvD,MAAM,MAAM,YAAY,GAAG,KAAK;QAEhC,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,KAAK,oBAAoB;YACpC,IAAI,OAAO,UAAU;YACrB,MAAM,OAAO,UAAU,WAAW,QAAQ,MAAM,QAAQ;YACxD,QAAQ,OAAO,QAAQ,CAAC,QAAQ;YAChC,UAAU;YACV,SAAS,SAAS,EAAE;YACpB,cAAc,SAAS,MAAM;YAC7B,MAAM;YACN,YAAY;gBAAE,UAAU;gBAAM,GAAG,QAAQ;YAAC;QAC5C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;QAC3D,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,MAAM,MAAM,YAAY,GAAG,KAAK;QAChC,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,KAAK,oBAAoB;YACpC,IAAI,OAAO,UAAU;YACrB,MAAM,OAAO,UAAU,WAAW,QAAQ,MAAM,QAAQ;YACxD,QAAQ,OAAO,QAAQ,CAAC,QAAQ;YAChC,UAAU;YACV,SAAS;YACT,cAAc;YACd,MAAM;YACN,YAAY;gBACV,UAAU;gBACV,OAAO,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;gBACnD,GAAG,QAAQ;YACb;QACF;QACA,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;YAC/B,OAAO;YACP,YAAY;gBAAE,UAAU;YAAK;QAC/B;QACA,MAAM;IACR;AACF"}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 771, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/auth.ts"], "sourcesContent": ["import { API_ENDPOINTS } from \"@/api/config\";\r\nimport AzureADProvider from \"next-auth/providers/azure-ad\";\r\nimport { trackedFetch } from \"./trackApi\";\r\nimport { getAppInsights } from \"./appInsights\";\r\n\r\nexport const authOptions = {\r\n  providers: [\r\n    AzureADProvider({\r\n      clientId: process.env.RECRUITER_SSO_CLIENT_ID! as string,\r\n      clientSecret: process.env.RECRUITER_SSO_CLIENT_SECRET! as string,\r\n      tenantId: process.env.NEXT_PUBLIC_AZURE_TENANT_ID! as string,\r\n    }),\r\n  ],\r\n  secret: process.env.NEXTAUTH_SECRET!,\r\n  pages: {\r\n    signIn: `${process.env.NEXTAUTH_URL}/login`, // Custom sign-in page URL\r\n  },\r\n  callbacks: {\r\n    async redirect({ url, baseUrl }: { url: string; baseUrl: string }) {\r\n      if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\r\n      if (new URL(url).origin === baseUrl) return url;\r\n      return baseUrl;\r\n    },\r\n\r\n    async session({ session }: any) {\r\n      if (\r\n        process.env.NEXT_PUBLIC_AD_LOGIN !== \"true\" &&\r\n        process.env.IS_ENTITLEMENT_ENABLED !== \"true\"\r\n      ) {\r\n        return session;\r\n      }\r\n      const portal_name = \"recruiter\";\r\n      let entitlement = {};\r\n      try {\r\n        // Caling an entitlements API to fetch user entitlements\r\n        const url = `${\r\n          API_ENDPOINTS.getEntitlements\r\n        }?email_id=${encodeURIComponent(\r\n          session?.user?.email\r\n        )}&portal_name=${encodeURIComponent(portal_name)}`;\r\n        const res = await trackedFetch(url, {}, { context: \"getEntitlements\" });\r\n\r\n        if (res.ok) {\r\n          const data = await res.json();\r\n          entitlement = data.entitlement;\r\n          getAppInsights()?.trackEvent({\r\n            name: \"FE_Entitlements_Fetched\",\r\n            properties: {\r\n              email: session?.user?.email,\r\n            },\r\n          });\r\n        } else {\r\n          console.error(\"Failed to fetch entitlements:\", res.statusText);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error fetching entitlements:\", err);\r\n      }\r\n      return {\r\n        ...session,\r\n        entitlement,\r\n      };\r\n    },\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAAc;IACzB,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAe,AAAD,EAAE;YACd,UAAU,QAAQ,GAAG,CAAC,uBAAuB;YAC7C,cAAc,QAAQ,GAAG,CAAC,2BAA2B;YACrD,QAAQ;QACV;KACD;IACD,QAAQ,QAAQ,GAAG,CAAC,eAAe;IACnC,OAAO;QACL,QAAQ,GAAG,QAAQ,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC;IAC7C;IACA,WAAW;QACT,MAAM,UAAS,EAAE,GAAG,EAAE,OAAO,EAAoC;YAC/D,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;YAClD,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;YAC5C,OAAO;QACT;QAEA,MAAM,SAAQ,EAAE,OAAO,EAAO;YAC5B,uCAGE;;YAEF;YACA,MAAM,cAAc;YACpB,IAAI,cAAc,CAAC;YACnB,IAAI;gBACF,wDAAwD;gBACxD,MAAM,MAAM,GACV,+GAAA,CAAA,gBAAa,CAAC,eAAe,CAC9B,UAAU,EAAE,mBACX,SAAS,MAAM,OACf,aAAa,EAAE,mBAAmB,cAAc;gBAClD,MAAM,MAAM,MAAM,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,CAAC,GAAG;oBAAE,SAAS;gBAAkB;gBAErE,IAAI,IAAI,EAAE,EAAE;oBACV,MAAM,OAAO,MAAM,IAAI,IAAI;oBAC3B,cAAc,KAAK,WAAW;oBAC9B,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;wBAC3B,MAAM;wBACN,YAAY;4BACV,OAAO,SAAS,MAAM;wBACxB;oBACF;gBACF,OAAO;oBACL,QAAQ,KAAK,CAAC,iCAAiC,IAAI,UAAU;gBAC/D;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,gCAAgC;YAChD;YACA,OAAO;gBACL,GAAG,OAAO;gBACV;YACF;QACF;IACF;AACF"}}, {"offset": {"line": 834, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/app/api/entitlements/route.ts"], "sourcesContent": ["import { fetchEntitlements } from \"@/api/serverActions\";\r\nimport { getServerSession } from \"next-auth\";\r\nimport { authOptions } from \"@/library/auth\";\r\n\r\nexport async function GET() {\r\n  try {\r\n    const session = await getServerSession(authOptions);\r\n    const entitlementData = await fetchEntitlements(session?.user?.email || \"\");\r\n    return Response.json(entitlementData);\r\n  } catch (error) {\r\n    console.error(\"Error fetching entitlement data:\", error);\r\n    return new Response(\"Internal Server Error\", { status: 500 });\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,iHAAA,CAAA,cAAW;QAClD,MAAM,kBAAkB,MAAM,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,MAAM,SAAS;QACxE,OAAO,SAAS,IAAI,CAAC;IACvB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO,IAAI,SAAS,yBAAyB;YAAE,QAAQ;QAAI;IAC7D;AACF"}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}