import { CircleX, Expand, Minimize, Copy } from "lucide-react";
import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "../ui/button";
import {
  Candidate,
  Vacancy,
  WhyFitReasonPayload,
} from "@/app/candidates/helper";
import { useNotification } from "@/hooks/useNotification";
import Modal from "@/components/Modal";
import AppToolTip from "../AppToolTip";
import Loading from "../Loading";
import { IS_WHY_FIT_EDITABLE } from "@/api/config";

const WhyFitAction = ({
  candidate,
  setCandidates,
  selectedVacancy,
  existingEdit,
  setExistingEdit,
  candidateEditId,
  setCandidateEditId,
  candidateEditId2,
  setCandidateEditId2,
  setExpandPopupOpen,
}: {
  candidate: Candidate;
  setCandidates?: React.Dispatch<React.SetStateAction<Candidate[]>>;
  selectedVacancy: Vacancy;
  existingEdit: boolean;
  setExistingEdit: React.Dispatch<React.SetStateAction<boolean>>;
  candidateEditId: string;
  setCandidateEditId: React.Dispatch<React.SetStateAction<string>>;
  candidateEditId2: string;
  setCandidateEditId2: React.Dispatch<React.SetStateAction<string>>;
  setExpandPopupOpen: React.Dispatch<React.SetStateAction<boolean>>;
}) => {
  const [showCommentBox, setShowCommentBox] = useState(false);

  const commentBoxRef = useRef<HTMLDivElement | null>(null);
  const buttonRef = useRef<HTMLDivElement | null>(null);
  const [openAbove, setOpenAbove] = useState(false);
  const [confirmModal, setConfirmModal] = useState(false);
  const { showNotification } = useNotification();
  const [originalComment, setOriginalComment] = useState(
    candidate?.candidate_data?.current_fitness_reason?.reason ?? ""
  );
  const [comment, setComment] = useState(
    candidate?.candidate_data?.current_fitness_reason?.reason ?? ""
  );

  const [loading, setLoading] = useState(false);
  const expandedTextareaRef = useRef<HTMLTextAreaElement | null>(null);

  useEffect(() => {
    if (showCommentBox && expandedTextareaRef.current) {
      const textarea = expandedTextareaRef.current;
      // Wait for DOM to paint
      requestAnimationFrame(() => {
        textarea.focus();
        textarea.setSelectionRange(
          textarea.value.length,
          textarea.value.length
        );
      });
    }
  }, [showCommentBox]);

  useEffect(() => {
    const decision = candidate?.candidate_data?.current_fitness_reason;
    if (decision) {
      setComment(decision.reason ?? "");
      setOriginalComment(decision.reason ?? "");
    }
  }, [candidate]);

  useEffect(() => {
    if (comment && comment !== originalComment) {
      setExistingEdit(true);
    } else {
      setExistingEdit(false);
    }
  }, [comment]);

  useEffect(() => {
    if (showCommentBox && buttonRef.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;
      setOpenAbove(buttonRect.bottom + 220 > windowHeight);
    }
  }, [showCommentBox]);

  const handleReviewClick = () => {
    setShowCommentBox((prev) => {
      if (!prev) {
        setOriginalComment(
          candidate?.candidate_data?.current_fitness_reason?.reason ?? ""
        );
      }
      return !prev;
    });
  };

  const handleCopy = () => {
    if (!comment) {
      showNotification("Nothing to copy.", "warning");
      return;
    }
    navigator.clipboard
      .writeText(comment)
      .then(() => {
        showNotification("Copied to clipboard!", "success");
      })
      .catch(() => {
        showNotification("Failed to copy.", "error");
      });
  };

  const handleSend = async () => {
    setConfirmModal(false);
    setExistingEdit(false);
    setShowCommentBox(false);
    setCandidateEditId("");
    setCandidateEditId2("");
    setLoading(true);
    const requestObj: WhyFitReasonPayload = {
      candidate_contact_id: candidate.candidate_contactid,
      vacancy_refno: selectedVacancy?.refno,
      fitness_reason_text: comment,
      author_email: selectedVacancy.locked_by ?? "",
    };
    try {
      let response: any = await fetch("/api/candidates/fitness_reason", {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestObj),
      });
      response = await response.json();
      if (response?.status_code === 200) {
        showNotification("Comment submitted successfully!", "success");
        if (setCandidates) {
          setCandidates((prev) =>
            prev.map((c) =>
              c.candidate_contactid === candidate.candidate_contactid
                ? {
                    ...c,
                    candidate_data: {
                      ...c.candidate_data,
                      current_fitness_reason: {
                        reason: comment,
                        author:
                          candidate.candidate_data.current_fitness_reason
                            ?.author || "",
                        timestamp:
                          candidate.candidate_data.current_fitness_reason
                            ?.timestamp || "",
                      },
                    },
                  }
                : c
            )
          );
        }
      } else {
        showNotification(
          "Error submitting comment. Please try again.",
          "error"
        );
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.error(error);
      showNotification("Error submitting comment. Please try again.", "error");
    }
  };

  const handleSave = () => {
    if (comment.length > 0) {
      handleSend();
    } else {
      setConfirmModal(true);
    }
  };

  function cancelRevertChanges() {
    setCandidateEditId2("");
  }

  function confirmDiscardChanges() {
    setExistingEdit(false);
    setShowCommentBox(false);
    setComment(originalComment);
    setCandidateEditId("");
    setCandidateEditId2("");
  }

  useEffect(() => {
    if (showCommentBox) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "auto";
    }
    setExpandPopupOpen(showCommentBox);

    return () => {
      document.body.style.overflow = "auto"; // Clean up on unmount
    };
  }, [showCommentBox]);

  const isEditable =
    IS_WHY_FIT_EDITABLE === "false"
      ? !(IS_WHY_FIT_EDITABLE === "false")
      : !selectedVacancy?.is_locked && selectedVacancy?.locked_by;

  return (
    <div className="flex flex-col items-center w-full" ref={buttonRef}>
      <div className="flex items-center space-x-2 relative w-full">
        <motion.div className="cursor-pointer w-full">
          <textarea
            cols={3}
            value={comment}
            onChange={(e) => {
              setCandidateEditId2(candidate.candidate_contactid);
              if (
                existingEdit &&
                candidateEditId !== candidate.candidate_contactid
              ) {
                return;
              }
              setCandidateEditId(candidate.candidate_contactid);
              setComment(e.target.value);
            }}
            className="w-full border border-gray-300 rounded-lg p-2 my-1"
            disabled={!isEditable}
          />
          <div className="flex gap-1.5 mb-1">
            {comment !== originalComment && (
              <>
                <Button
                  onClick={() => confirmDiscardChanges()}
                  className="bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0 h-6 w-11 text-[10px]"
                >
                  Discard
                </Button>

                <Button
                  size="sm"
                  onClick={() => handleSave()}
                  disabled={!isEditable || comment === originalComment}
                  className="bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0 h-6 w-11 text-[10px]"
                >
                  {loading ? <Loading /> : "Save"}
                </Button>
              </>
            )}
            <div className="absolute -top-0.5 -right-0.5">
              <AppToolTip
                text="Click To Expand"
                header={
                  <Expand
                    onClick={() => handleReviewClick()}
                    size={8}
                    className="bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0 h-4 w-4 text-[6px]"
                  />
                }
              />
            </div>
          </div>
        </motion.div>
      </div>
      <AnimatePresence>
        {showCommentBox && (
          <div className="fixed top-0 left-0 w-[50%] h-full z-[999] bg-[#33333320] flex justify-center items-center">
            <motion.div
              ref={commentBoxRef}
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ duration: 0.25 }}
              className="fixed top-[40vh] left-[45vw] w-[500px] h-[300px] bg-white shadow-lg rounded-lg p-4 border"
            >
              <div className="flex justify-between items-center mb-1">
                <p className="text-sm font-semibold">Why-Fit</p>
                <div className="flex gap-2">
                  <AppToolTip
                    text="Copy Text"
                    header={
                      <Copy
                        onClick={handleCopy}
                        className="bg-gray-800 text-white p-1 rounded hover:bg-gray-900 transition size-5"
                      />
                    }
                  />
                  <AppToolTip
                    text="Click To Minimize"
                    header={
                      <Minimize
                        onClick={() => setShowCommentBox(false)}
                        className="bg-gray-800 text-white p-1 rounded hover:bg-gray-900 transition size-5"
                      />
                    }
                  />
                </div>
              </div>

              <textarea
                ref={expandedTextareaRef}
                autoFocus
                disabled={!isEditable}
                className={`w-full ${
                  isEditable ? "h-[200px]" : "h-[235px]"
                } border p-2 rounded resize-none focus:outline-none focus:ring-2 focus:ring-blue-500`}
                placeholder="Share your thoughts..."
                value={comment}
                onChange={(e) => setComment(e.target.value)}
              />

              {isEditable && (
                <div className="flex justify-end gap-1.5 mt-2">
                  <Button
                    onClick={confirmDiscardChanges}
                    className="bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition"
                    size="sm"
                  >
                    Discard
                  </Button>
                  <Button
                    onClick={handleSave}
                    disabled={comment === originalComment}
                    className="bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition"
                    size="sm"
                  >
                    Save {loading && <Loading />}
                  </Button>
                </div>
              )}
            </motion.div>
          </div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {confirmModal && (
          <motion.div
            ref={commentBoxRef}
            initial={{ opacity: 0, y: openAbove ? -10 : 10, x: -10 }}
            animate={{ opacity: 1, y: 0, x: -50 }}
            exit={{ opacity: 0, y: openAbove ? -10 : 10 }}
            transition={{ duration: 0.3 }}
            className={`absolute z-[9] ${
              openAbove ? "bottom-10" : "top-10"
            } w-[300px] h-[170px] bg-white shadow-lg rounded-lg p-4 border`}
          >
            <div className="flex justify-between items-center mb-1">
              <p className="text-sm font-semibold">Please confirm</p>
              <CircleX
                size={14}
                className="cursor-pointer"
                onClick={() => setConfirmModal(false)}
              />
            </div>
            <p className="text-sm font-semibold">
              Comments are empty. Please confirm to submit.
            </p>

            {/* {viewState ? null : ( */}
            <div className="flex gap-4">
              <Button
                onClick={() => handleSend()}
                className="w-full bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0"
              >
                Confirm
              </Button>
              <Button
                onClick={() => setConfirmModal(false)}
                className="w-full bg-gray-800 text-white py-1 rounded hover:bg-gray-900 transition px-0 ml-0"
              >
                Cancel
              </Button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <Modal
        isOpen={Boolean(
          candidateEditId &&
            candidateEditId2 &&
            candidateEditId === candidate.candidate_contactid &&
            candidateEditId2 !== candidate.candidate_contactid
        )}
        onClose={() => setCandidateEditId2("")}
        title="Changes not saved!"
      >
        <p className="mb-5 h-20">
          Your recent change to the why fit field has not been saved yet. Choose
          &lsquo;Cancel&lsquo; to continue text changes or &lsquo;Discard
          Chanages&lsquo; to revert or &lsquo;Save&lsquo; your changes.
        </p>
        <div className="flex justify-end gap-1.5">
          <Button
            onClick={() => {
              handleSave();
            }}
            className="rounded-md"
            style={{
              color: "green",
              border: "1px solid green",
              background: "#fff",
            }}
          >
            Save {loading && <Loading />}
          </Button>
          <Button
            onClick={() => {
              confirmDiscardChanges();
            }}
            className="rounded-md"
            style={{
              color: "#e95151",
              border: "1px solid #e95151",
              background: "#fff",
            }}
          >
            Discard Changes
          </Button>
          <Button
            onClick={() => {
              cancelRevertChanges();
            }}
            style={{ background: "white" }}
            className="text-gray-900 border border-gray-900 rounded-md"
          >
            Cancel
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default WhyFitAction;
