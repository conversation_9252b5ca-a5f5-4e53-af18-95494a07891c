{"/page": "app/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/subcategories/route": "app/api/subcategories/route.js", "/candidates/page": "app/candidates/page.js", "/api/vacancies/route": "app/api/vacancies/route.js", "/api/vacancies/[id]/route": "app/api/vacancies/[id]/route.js", "/api/entitlements/route": "app/api/entitlements/route.js", "/_not-found/page": "app/_not-found/page.js", "/api/vacancies/files/route": "app/api/vacancies/files/route.js", "/api/vacancies/shortlisted/route": "app/api/vacancies/shortlisted/route.js", "/CandidateTuning/For_Mercury_Portal/page": "app/CandidateTuning/For_Mercury_Portal/page.js", "/api/vacancies/resume/[id]/route": "app/api/vacancies/resume/[id]/route.js"}