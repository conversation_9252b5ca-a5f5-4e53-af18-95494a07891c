import { fetchEntitlements } from "@/api/serverActions";
import { getServerSession } from "next-auth";
import { authOptions } from "@/library/auth";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    const entitlementData = await fetchEntitlements(
      session?.user?.email || "",
      true
    );
    return Response.json(entitlementData);
  } catch (error) {
    console.error("Error fetching entitlement data:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
