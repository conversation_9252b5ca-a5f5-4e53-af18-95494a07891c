import { ResumeData, WorkExperience } from "@/app/candidates/helper";
import React from "react";
import Loading from "../Loading";
import { DownloadIcon, ExternalLink } from "lucide-react";
import { isParsedResume } from "@/api/config";
import { Button } from "../ui/button";
import { Vacancy } from "../CandidateTable/helper";
import { getAppInsights } from "@/library/appInsights";

const showSoftSkills = process.env.NEXT_PUBLIC_IS_SHOW_SOFT_SKILLS_ENABLED === "true";
const tempResumeData = {
    "candidate_id": 122515,
    "status": 0,
    "description": "Success",
    "candidate": {
        "name": "rakesh V",
        "email": "<EMAIL>",
        "phone": "+10000096534",
        "contact": {
            "resume": {
                "name": "rakesh V",
                "email": "<EMAIL>",
                "phone": "+10000096534",
                "easyOCR": false
            }
        },
        "created": "2025-02-12T02:50:12",
        "md5hash": "53bfb8d05ad17b04ba1eb32013b9d4a3",
        "modified": "2025-05-23T02:50:12",
        "contactid": "56f5d545-e1af-ef11-b8e8-000d3a8f4f54",
        "failedpdf": "",
        "city": "test city",
        "state": "test state",
        "job title": [
            "graphic designer",
            "graphic designer",
            "graphic designer",
            "graphic designer",
            "graphic designer",
            "graphic designer",
            "graphic designer",
            "graphic designer",
            "graphic designer",
            "graphic designer",
            "graphic designer",
            "graphic designer",
            ""
        ],
        "skip_phone": true,
        "resume_file": "",
        "soft skills": [
            "teamwork",
            "communication"
        ],
        "resume_status": "Done Processing",
        "work experience": [
            {
                "title": "Graphic Designer",
                "end_date": "",
                "start_date": "'23",
                "description": "digital ads, photo manipulation",
                "company_name": "Inspire Brands"
            },
            {
                "title": "Graphic Designer",
                "end_date": "",
                "start_date": "'22",
                "description": "social posts, .gif animation, informational",
                "company_name": "PT Solutions"
            },
            {
                "title": "Graphic Designer",
                "end_date": "'22",
                "start_date": "'21",
                "description": "product layout for email marketing campaigns",
                "company_name": "Carters, Inc."
            },
            {
                "title": "Graphic Designer",
                "end_date": "",
                "start_date": "'20",
                "description": "sermon series themes & digital/print promotionals, wayfinders, vehicle wraps",
                "company_name": "Victory Church"
            },
            {
                "title": "Graphic Designer",
                "end_date": "",
                "start_date": "'21",
                "description": "brand-identity development, motion graphics, iconography, web layouts, photo manipulation",
                "company_name": "Cox Enterprises"
            },
            {
                "title": "Graphic Designer",
                "end_date": "",
                "start_date": "'19",
                "description": "real estate brochures, package/logo/t-shirt design, email layouts",
                "company_name": "Beacham & Co."
            },
            {
                "title": "Graphic Designer",
                "end_date": "",
                "start_date": "'21",
                "description": "product-video storyboards, web layouts, presentations, digital/print ads",
                "company_name": "Big Red Rooster"
            },
            {
                "title": "Graphic Designer",
                "end_date": "",
                "start_date": "'19",
                "description": "logo/package/training-manual design, digital ads, pre-press mgmt.",
                "company_name": "NAMB"
            },
            {
                "title": "Graphic Designer",
                "end_date": "",
                "start_date": "'21",
                "description": "digital/print ads, informational",
                "company_name": "Cox Enterprises"
            },
            {
                "title": "Graphic Designer",
                "end_date": "'19",
                "start_date": "'18",
                "description": "report/t-shirt design, web layout, presentations, digital ads",
                "company_name": "JM Huber Corp."
            },
            {
                "title": "Graphic Designer",
                "end_date": "",
                "start_date": "'20",
                "description": "digital/print ads, web/email layouts, promotional materials",
                "company_name": "Stoneridge Group"
            },
            {
                "title": "Graphic Designer",
                "end_date": "'19",
                "start_date": "'18",
                "description": "editorial design, photo manipulation, infographics, logo/t-shirt design",
                "company_name": "Conway, Inc."
            },
            {
                "title": "",
                "end_date": "'17",
                "start_date": "'12",
                "description": "worked a variety of production roles for screenprint, pre-press, & signage.",
                "company_name": ""
            }
        ],
        "technical skills": [
            "graphic design",
            "photo manipulation",
            "animation",
            "layout design"
        ],
        "tools and platforms": [
            "Adobe Illustrator",
            "Adobe InDesign",
            "Adobe Photoshop",
            "Adobe After Effects",
            "Adobe XD"
        ],
        "degrees and certifications": [
            "BFA"
        ]
    }
};

const SkillBadge = ({
  skill,
  styleClass,
}: {
  skill: string;
  styleClass?: string;
}) => {
  return (
    <span
      className={`px-3 py-1 rounded-full text-xs font-semibold text-gray-800 border border-gray-300 shadow-sm ${
        styleClass ?? ""
      }`}
    >
      {skill}
    </span>
  );
};

const CandidateResume = ({
  vacancy,
  selectedResume,
  setSelectedResume,
  isResumeModalOpen,
  setIsResumeModalOpen,
  isLoading,
}: {
  vacancy: Vacancy | null;
  selectedResume: ResumeData | null;
  setSelectedResume: React.Dispatch<React.SetStateAction<null | ResumeData>>;
  isResumeModalOpen: boolean;
  setIsResumeModalOpen: React.Dispatch<React.SetStateAction<boolean>>;
  isLoading: boolean;
}) => {
  const cv = tempResumeData?.candidate?.resume_file;

  const legendSkillWeights = ["high", "medium", "normal"];

  const getSharePointURL = (url: string) => {
    url = url.toLocaleLowerCase();
    const baseURL = url.split("/contact")[0] || "";
    const idMatch = url.match(/\/sites.*$/);
    const encodedFileName = idMatch ? encodeURIComponent(idMatch[0]) : "";
    return `${baseURL}/_layouts/15/embed.aspx?Id=${encodedFileName}`;
  };

  const resultURL = cv ? getSharePointURL(cv) : "";

  const getFullWorkExperience = (tempResumeData: ResumeData | null) => {
    return (
      <div className="mb-6 p-4 bg-gray-100 rounded-lg">
        <h3 className="text-xl font-bold text-gray-900 mb-2">
          Full Work Experience
        </h3>
        {tempResumeData?.candidate?.["work experience"].length! > 0 ? (
          <ul className="list-disc ml-5 space-y-3">
            {tempResumeData?.candidate?.["work experience"].map(
              (exp: WorkExperience, index: number) => (
                <li key={index} className="p-3 bg-white rounded-md shadow">
                  <p className="font-semibold text-lg">
                    {exp.title} at {exp.company}
                  </p>
                  <p className="text-gray-600 text-sm">
                    {exp.start_date} - {exp.end_date || "Present"}
                  </p>
                  <p className="text-gray-700 max-h-[150px] overflow-y-auto">
                    {(() => {
                      const description = exp.description || "";
                      const softSkills =
                        vacancy?.vacancy_data?.["soft skills"] || [];
                      const technicalSkills =
                        vacancy?.vacancy_data?.["technical skills"] || [];
                      const toolsAndPlatforms =
                        vacancy?.vacancy_data?.["tools and platforms"] || [];
                      const allSkills = [
                        ...softSkills,
                        ...technicalSkills,
                        ...toolsAndPlatforms,
                      ];
                      const regex = new RegExp(
                        allSkills
                          .map(
                            (skill) =>
                              `\\b${skill.name.replace(
                                /[.*+?^${}()|[\]\\]/g,
                                "\\$&"
                              )}\\b`
                          )
                          .join("|"),
                        "gi"
                      );
                      const result: (string | React.ReactNode)[] = [];
                      let lastIndex = 0;
                      let match: RegExpExecArray | null;
                      while ((match = regex.exec(description)) !== null) {
                        if (match.index > lastIndex) {
                          result.push(
                            description.slice(lastIndex, match.index)
                          );
                        }
                        const matchedSkill = allSkills.find((skill) => {
                          const skillName =
                            typeof skill === "string" ? skill : skill.name;
                          return (
                            skillName &&
                            match &&
                            match[0] &&
                            skillName.toLowerCase() === match[0].toLowerCase()
                          );
                        });
                        const weight =
                          typeof matchedSkill === "object" &&
                          matchedSkill !== null
                            ? matchedSkill.weight
                            : undefined;
                        const colorClass = weight
                          ? getHighlightStyle(weight)
                          : "bg-gray-200 text-gray-700 border border-gray-300";
                        result.push(
                          <span
                            key={match.index + "-" + match[0]}
                            className={colorClass}
                          >
                            {match[0]}
                          </span>
                        );
                        lastIndex = regex.lastIndex;
                      }
                      if (lastIndex < description.length) {
                        result.push(description.slice(lastIndex));
                      }
                      return result;
                    })()}
                  </p>
                </li>
              )
            )}
          </ul>
        ) : (
          <p className="text-gray-500">No work experience available</p>
        )}
      </div>
    );
  };

  const getSkillBadgeHighlightClassName = (skill: string) => {
    if (!skill) return "";
    const skillLower = skill.toLowerCase();
    const softSkills = vacancy?.vacancy_data?.["soft skills"] || [];
    const technicalSkills = vacancy?.vacancy_data?.["technical skills"] || [];
    const toolsAndPlatforms =
      vacancy?.vacancy_data?.["tools and platforms"] || [];
    const allSkills = [...softSkills, ...technicalSkills, ...toolsAndPlatforms];
    const matchedSkill = allSkills.find(
      (skill) => skill.name.toLowerCase() === skillLower
    );
    return matchedSkill ? getHighlightStyle(matchedSkill.weight) : "";
  };

  const getParsedResume = () => {
    return (
      <>
        {cv ? (
          <>
            <div className="mb-6">
              <div className="flex justify-between my-4">
                <h3 className="text-xl font-bold text-gray-900">CV</h3>
                <a
                  href={cv}
                  target="_blank"
                  download
                  onClick={() => {
                    getAppInsights()?.trackEvent({
                      name: "FE_DownloadCVClicked",
                      properties: { resume: cv },
                    });
                  }}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-4 py-2 rounded-lg transition-all flex items-center gap-2 z-10"
                >
                  Download CV <DownloadIcon />
                </a>
              </div>
              <iframe
                className="cv-renderer"
                src={resultURL}
                width="100%"
                height="600px"
              />
            </div>
          </>
        ) : (
          <div className="flex relative justify-center items-center text-3xl my-5">
            Resume is not available
          </div>
        )}
      </>
    );
  };

  // Add getHighlightStyle function for color based on weight
  const getHighlightStyle = (weight: string) => {
    switch (weight) {
      case "high":
        return "p-1 bg-green-200/50 text-green-800 border border-green-300 shadow-md";
      case "medium":
        return "p-1 bg-blue-200/50 text-blue-800 border border-blue-300 shadow-md";
      case "normal":
        return "p-1 bg-yellow-200/50 text-yellow-800 border border-yellow-300 shadow-md";
      case "low":
        return "p-1 bg-red-200/50 text-red-800 border border-red-300 shadow-md";
      default:
        return "p-1 bg-gray-200 text-gray-700 border border-gray-300";
    }
  };
  const handleResumeLinkClick = () => {
    getAppInsights()?.trackEvent({
      name: "FE_ResumeLinkClicked",
      properties: { resume: cv },
    });
  };

  return (
    <div>
      {" "}
      {/* Modal */}
      {isResumeModalOpen && (
        <div className="fixed z-50 inset-0 bg-gray-900 bg-opacity-60 flex items-center justify-center">
          <div className="bg-white w-[80vw] min-h-[60vh] px-8 rounded-lg max-h-[90vh] overflow-y-auto shadow-lg">
            {/* Header */}
            <div className="flex items-center justify-between text-gray-900 border-b pb-2 mb-4 sticky top-0 bg-white z-10 pt-8">
              <h2 className="text-2xl font-extrabold ">Candidate Profile</h2>
              <div className="flex">
                <Button
                  asChild
                  className="text-blue-500 text-lg"
                  variant="link"
                >
                  <a
                    href={cv}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={handleResumeLinkClick}
                  >
                    Resume Link <ExternalLink />
                  </a>
                </Button>
                {/* Close Button */}
                <button
                  onClick={() => {
                    setSelectedResume(null);
                    setIsResumeModalOpen(false);
                  }}
                  className="bg-red-600 hover:bg-red-700 text-white font-semibold px-6 py-2 rounded-lg transition-all"
                >
                  Close
                </button>
              </div>
            </div>
            {tempResumeData?.candidate &&
            Object.keys(tempResumeData.candidate)?.length > 0 ? (
              <>
                {/* Personal Details */}
                <div className="mb-6">
                  <p className="text-lg font-semibold text-gray-700">
                    <span className="text-gray-900">👤 Name:</span>{" "}
                    {tempResumeData?.candidate?.name}
                  </p>
                  <p className="text-lg font-semibold text-gray-700">
                    <span className="text-gray-900">✉️ Email:</span>{" "}
                    {tempResumeData?.candidate?.email}
                  </p>
                  <p className="text-lg font-semibold text-gray-700">
                    <span className="text-gray-900">📞 Phone:</span>{" "}
                    {tempResumeData?.candidate?.phone}
                  </p>
                  {tempResumeData?.candidate?.city &&
                    tempResumeData?.candidate?.state && (
                      <p className="text-lg font-semibold text-gray-700">
                        <span className="text-gray-900">📍 location:</span>{" "}
                        {`${tempResumeData?.candidate?.city}, ${tempResumeData?.candidate?.state}`}
                      </p>
                    )}
                </div>

                {/* Skills & Certifications */}
                <div className="mb-6">
                  <p className="flex justify-content items-start mb-5">
                    <strong className="w-[250px]"> Skills</strong>
                    <span className="flex-1 flex flex-wrap gap-2 capitalize">
                      {legendSkillWeights.map((weight, index) => (
                        <SkillBadge
                          key={index}
                          skill={weight}
                          styleClass={getHighlightStyle(weight)}
                        />
                      ))}
                    </span>
                  </p>
                  {/* Soft Skills */}
                  {showSoftSkills && (
                    <p className="flex items-start mb-5">
                      <strong className="w-[250px]">🗣 Soft Skills:</strong>
                      <span className="flex-1 flex flex-wrap gap-2 capitalize">
                        {tempResumeData?.candidate?.["soft skills"]?.length > 0
                          ? tempResumeData?.candidate?.["soft skills"].map(
                              (skill, index) => (
                                <SkillBadge
                                  key={index}
                                  skill={skill}
                                  styleClass={getSkillBadgeHighlightClassName(
                                    skill
                                  )}
                                />
                              )
                            )
                          : "N/A"}
                      </span>
                    </p>
                  )}

                  {/* Technical Skills */}
                  <p className="flex items-start mb-5">
                    <strong className="w-[250px]">💻 Technical Skills:</strong>
                    <span className="flex-1 flex flex-wrap gap-2 capitalize">
                      {tempResumeData?.candidate?.["technical skills"]?.length >
                      0
                        ? tempResumeData?.candidate?.["technical skills"]?.map(
                            (skill, index) => (
                              <SkillBadge
                                key={index}
                                skill={skill}
                                styleClass={getSkillBadgeHighlightClassName(
                                  skill
                                )}
                              />
                            )
                          )
                        : "N/A"}
                    </span>
                  </p>

                  {/* Tools & Platforms */}
                  <p className="flex items-start mb-5">
                    <strong className="w-[250px]">🛠 Tools & Platforms:</strong>
                    <span className="flex-1 flex flex-wrap gap-2 capitalize">
                      {tempResumeData?.candidate?.["tools and platforms"]
                        ?.length > 0
                        ? tempResumeData?.candidate?.[
                            "tools and platforms"
                          ]?.map((skill, index) => (
                            <SkillBadge
                              key={index}
                              skill={skill}
                              styleClass={getSkillBadgeHighlightClassName(
                                skill
                              )}
                            />
                          ))
                        : "N/A"}
                    </span>
                  </p>
                </div>

                {!isParsedResume
                  ? getParsedResume()
                  : getFullWorkExperience(tempResumeData)}
              </>
            ) : tempResumeData?.candidate &&
              Object.keys(tempResumeData.candidate)?.length === 0 ? (
              <div className="flex relative top-28 pt-10 justify-center items-center text-3xl">
                Resume details not found
              </div>
            ) : isResumeModalOpen && isLoading ? (
              <Loading height="h-[80vh]" />
            ) : null}
          </div>
        </div>
      )}
    </div>
  );
};

export default CandidateResume;
