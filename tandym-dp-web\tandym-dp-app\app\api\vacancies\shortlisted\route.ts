import { NextRequest, NextResponse } from "next/server";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/utils/auth-utils";
import { APPLICATION_NAVIGATION_ROUTES, FEATURE_NAMES } from "@/library/utils";
import {
  postHistoricalLogs,
  postVacanciesShortlisted,
} from "@/api/serverActions";
import { getEntitlementData } from "@/app/actions/getEntitlementData";

export async function POST(req: NextRequest) {
  // ✅ 1️⃣ Check auth
  // const response = await CheckAuth(APPLICATION_NAVIGATION_ROUTES.VACANCY);
  const response = true;

  // ✅ 2️⃣ Get entitlement from cookie
  const entitlementCookie = await getEntitlementData();
  console.log("entitlementCookie:::->", entitlementCookie);

  // ✅ 3️⃣ Read request body
  const data = await req.json();

  if (response) {
    try {
      // ✅ 4️⃣ Build payload
      const payload = {
        vacancy_id: data.vacancy_id,
        candidate_id: data.candidate_id,
        reviewer_email: data.reviewer_email,
      };

      // ✅ 5️⃣ Call shortlist API
      const success: any = await postVacanciesShortlisted(payload);

      if (!success || !success.data) {
        // ✅ 6️⃣ Log only if Historical_Logs === true
        if (entitlementCookie?.Historical_Logs === true) {
          await postHistoricalLogs(
            data.reviewer_email,
            data.portal_name,
            FEATURE_NAMES.SORTLIST_CANDIDATE_FEATURE,
            {
              success: false,
              message: "Failed to process shortlisted vacancy",
              payload: payload,
              detail: success?.data?.detail,
            }
          );
        }

        return NextResponse.json(
          { error: "Failed to process shortlisted vacancy" },
          { status: success?.status || 500 }
        );
      }

      // ✅ 7️⃣ Log success only if Historical_Logs === true
      if (entitlementCookie?.Historical_Logs === true) {
        await postHistoricalLogs(
          data.reviewer_email,
          data.portal_name,
          FEATURE_NAMES.SORTLIST_CANDIDATE_FEATURE,
          {
            success: true,
            message: "Shortlisted the candidate successfully for this vacancy",
            payload: payload,
          }
        );
      }

      return NextResponse.json(
        {
          message: "Shortlisted the candidate successfully for this vacancy",
          data: success.data,
        },
        { status: 200 }
      );
    } catch (error) {
      console.error("err coming", error);
      const err = error as {
        message?: string;
        data?: { detail?: string };
        status?: number;
      };

      // ✅ 8️⃣ Log error only if Historical_Logs === true
      if (entitlementCookie?.Historical_Logs === true) {
        await postHistoricalLogs(
          data.reviewer_email,
          data.portal_name,
          FEATURE_NAMES.SORTLIST_CANDIDATE_FEATURE,
          {
            success: false,
            message: "Failed to process shortlisted vacancy",
            payload: data,
            detail: err,
          }
        );
      }

      return NextResponse.json({
        error:
          err.message || "Failed to shortlist the candidate for this vacancy",
        detail: err.data?.detail,
        status_code: err.status,
      });
    }
  } else {
    return AuthErrorHandler();
  }
}
