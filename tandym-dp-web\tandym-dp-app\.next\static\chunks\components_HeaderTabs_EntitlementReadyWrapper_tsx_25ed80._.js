(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/components_HeaderTabs_EntitlementReadyWrapper_tsx_25ed80._.js", {

"[project]/components/HeaderTabs/EntitlementReadyWrapper.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__((__turbopack_import__) => {
    return Promise.all([
  "static/chunks/components_HeaderTabs_EntitlementReadyWrapper_tsx_b8c798._.js",
  "static/chunks/components_HeaderTabs_HeaderTabsMenu_tsx_30ea92._.js",
  "static/chunks/components_HeaderTabs_EntitlementReadyWrapper_tsx_e81cad._.js"
].map((chunk) => __turbopack_load__(chunk))).then(() => {
        return __turbopack_import__("[project]/components/HeaderTabs/EntitlementReadyWrapper.tsx [app-client] (ecmascript)");
    });
});
}}),
}]);