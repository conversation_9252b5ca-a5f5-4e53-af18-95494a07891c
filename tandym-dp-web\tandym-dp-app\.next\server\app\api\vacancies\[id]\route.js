const CHUNK_PUBLIC_PATH = "server/app/api/vacancies/[id]/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_2391a8._.js");
runtime.loadChunk("server/chunks/[root of the server]__13a328._.js");
runtime.loadChunk("server/chunks/_7d143d._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/vacancies/[id]/route/actions.js [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/api/vacancies/[id]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
