"use client";
import React, { useEffect, useState } from "react";
import {
  ArrowUpDown,
  ChevronLeft,
  ChevronRight,
  LockKeyhole,
} from "lucide-react";
import noCandidatesImage from "@/public/assets/noCandidates.svg";
import {
  Candidate,
  ResumeData,
  ReviewerConfig,
  Vacancy,
} from "../../app/candidates/helper";
import Loading from "@/components/Loading";
import { Input } from "@/components/ui/input";
import Image from "next/image";
import VacancyItem from "@/components/candidates/VacancyItem";
import CandidateResume from "@/components/candidates/CandidateResume";
import { useNotification } from "@/hooks/useNotification";
import CandidateTable from "../CandidateTable/CandidateTable";
import { useSession } from "next-auth/react";
import { usePathname } from "next/navigation";
import { Button } from "../ui/button";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { trackedFetch } from "@/library/trackApi";
import { getAppInsights } from "@/library/appInsights";
import VacancyDetails from "./VacancyDetails";
import { Badge } from "../ui/badge";
import { fetchEntitlements } from "@/api/serverActions";
import { NotificationProvider } from "@/hooks/useNotification";
import NotificationList from "../notifications/NotificationList";

const isVacancyFetchFromFiles = false 
  // process.env.NEXT_PUBLIC_IS_VACANCY_FETCH_FROM_FILES === "true";

const DEFAULT_PER_PAGE = 150;
const SHOW_SEARCH_BY_EMAIL = false;

const Candidates = ({
  vacancyid,
  mercuryPortal,
  emailId,
  IS_LOCK_FEATURE_DISABLED,
}: {
  vacancyid?: string;
  mercuryPortal?: boolean;
  emailId?: string;
  IS_LOCK_FEATURE_DISABLED: string;
}) => {
  const pathname = usePathname();
  const pageNotFound =
    vacancyid === undefined &&
    !pathname.includes(`/CandidateTuning/For_Mercury_Portal?vacancyid=`);
  const [vacancies, setVacancies] = useState<Vacancy[]>([]);
  const [vacancyCandidates, setVacancyCandidates] = useState<Record<
    string,
    Candidate[]
  > | null>({});
  const [selectedResume, setSelectedResume] = useState<ResumeData | null>(null);
  const [isResumeModalOpen, setIsResumeModalOpen] = useState(false);
  const [selectedVacancy, setSelectedVacancy] = useState<Vacancy | null>(null);
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [page, setPage] = useState<number>(1);
  const [limit, setLimit] = useState<number>(DEFAULT_PER_PAGE);
  const [search, setSearch] = useState<string>("");
  const [vacancySearch, setVacancySearch] = useState<string>("");
  const [activeVacancy, setActiveVacancy] = useState<Vacancy | null>(null);
  const [startRating, setStartRating] = useState(false);
  const [reviewLoading, setReviewLoading] = useState(false);
  const [completeRating, setCompleteRating] = useState(false);
  const [userName, setUserName] = useState<string>("");
  const [isParsedVacancyPopupOpen, setIsParsedVacancyPopupOpen] =
    useState<boolean>(false);
  const [sortConfig, setSortConfig] = useState<{
    key: string | null;
    direction: "asc" | "desc" | null;
  }>({
    key: null,
    direction: null,
  });
  const session = useSession();
  const name = session?.data?.user?.name;

  useEffect(() => {
    if (emailId) {
      try {
        const setEntitlement = async () => {
          await fetchEntitlements(emailId);
        };
        setEntitlement();
      } catch (error) {
        console.log("Error fetching entitlements::", error);
      }
      localStorage.setItem("emailId", emailId);
      setUserName(emailId);
    }
  }, [emailId]);

  const [sortConfigCan, setSortConfigCan] = useState<{
    key: string | null;
    direction: "asc" | "desc" | null;
  }>({
    key: null,
    direction: null,
  });

  const { showNotification } = useNotification();

  const openParsedVacancyPopup = () => {
    setActiveVacancy(selectedVacancy);
    setIsParsedVacancyPopupOpen(true);
  };

  const fetchVacancies = async () => {
    try {
      setLoading(true);
      const response = isVacancyFetchFromFiles
        ? await trackedFetch(
            "/api/vacancies/files",
            {},
            { context: "GetVacancies" }
          )
        : await trackedFetch("/api/vacancies", {}, { context: "GetVacancies" });
      if (response.ok) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const data: { vacancies: Vacancy[] } = await response.json();
        if (data.vacancies) {
          if (!isVacancyFetchFromFiles) {
            setVacancies(data.vacancies);
          }
        }
        getAppInsights()?.trackEvent({
          name: "FE_VacanciesFetched",
          properties: {
            count: data.vacancies?.length ?? 0,
            context: "GetVacancies",
          },
        });
        return data.vacancies;
      } else {
        console.error("Failed to fetch attributes:", response.statusText);
      }
    } catch (error) {
      console.error("Error fetching skills:", error);
      getAppInsights()?.trackException({
        error: new Error("vacancies api with error is " + error),
        severityLevel: 3,
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchCandidatesById = async (vacancyid: string) => {
    try {
      setLoading(true);
      if (isVacancyFetchFromFiles) {
        setCandidates(vacancyCandidates?.[vacancyid] || []);
        return;
      }
      const response = await trackedFetch(
        `/api/vacancies/${vacancyid.split("/").join("-")}`,
        {},
        { context: "GetCandidatesById" }
      );
      if (response.ok) {
        const data: { candidates: Candidate[] } = await response.json();
        setCandidates(data.candidates);
        getAppInsights()?.trackEvent({
          name: "FE_CandidatesFetchedById",
          properties: {
            vacancyid,
            context: "GetCandidatesByVacancyId",
          },
        });
      } else {
        console.error("Failed to fetch attributes:", response.statusText);
      }
    } catch (error) {
      console.error("Error fetching skills:", error);
      getAppInsights()?.trackException({
        error: new Error("CandidatesById api with error is " + error),
        severityLevel: 3,
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVacancies().then((vacancies) => {
      if (vacancies && vacancies.length > 0) {
        vacancies?.map((vacancy) => {
          if (vacancy?.vacancy_id?.toLowerCase() === vacancyid?.toLowerCase()) {
            setSelectedVacancy(vacancy);
          }
        });
      }
    });
    if (vacancyid && mercuryPortal) {
      fetchCandidatesById(vacancyid);
    }
  }, [vacancyid, mercuryPortal]);

  const handleSort = (key: string = "refno") => {
    let direction: "asc" | "desc" | null = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }

    const [externalSession, setExternalSession] = useState<{
      user: { name: string; email: string };
      token?: string;
    } | null>(null);

    const sortedData = [...vacancies].sort((a, b) => {
      if (a.refno < b.refno) return direction === "asc" ? -1 : 1;
      if (a.refno > b.refno) return direction === "asc" ? 1 : -1;
      return 0;
    });

    setSortConfig({ key, direction });
    setVacancies(sortedData);
  };

  const handleCandidateSort = (key: string) => {
    let direction: "asc" | "desc" = "asc";
    if (sortConfigCan.key === key && sortConfigCan.direction === "asc") {
      direction = "desc";
    }

    const getValue = (candidate: Candidate) => {
      const data = candidate.candidate_data;

      switch (key) {
        case "name":
          return data.name?.toLowerCase() || "";
        case "overallscore":
          return data["classification score"]?.overallscore ?? 0;
        case "jobtitlescore":
          return data["classification score"]?.jobtitlescore ?? 0;
        case "softskillsscore":
          return data["classification score"]?.softskillsscore ?? 0;
        case "technicalskills":
          return data["classification score"]?.["technical skills"] ?? 0;
        case "toolsplatformsscore":
          return data["classification score"]?.toolsplatformsscore ?? 0;
        case "degreesandcertifications":
          return (
            data["classification score"]?.["degrees and certifications"] ?? 0
          );
        case "industryexperiencescore":
          return data["classification score"]?.industryexperiencescore ?? 0;
        case "relevantexperiencescore":
          return data["classification score"]?.relevantexperiencescore ?? 0;
        case "jobtitle_recency_score":
          return data["classification score"]?.jobtitle_recency_score ?? 0;
        case "availability": {
          const availability = data.availability;
          if (!availability || availability === "Missing") return new Date(0);
          return new Date(availability);
        }
        case "city":
          const { city, state } = data;
          const isValid = (v: string | null | undefined) =>
            v && v !== "Missing";
          if (isValid(city) && isValid(state))
            return `${city}, ${state}`.toLowerCase();
          if (isValid(city)) return city.toLowerCase();
          if (isValid(state)) return state.toLowerCase();
          return null;
        case "freshness":
          return data["classification score"]?.jobtitle_recency_score ?? 0;
        case "freshness_index":
          return data["freshness_index"];
        default:
          return "";
      }
    };

    const sortedCandidates = [...candidates].sort((a, b) => {
      const aValue = getValue(a);
      const bValue = getValue(b);

      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return 1;
      if (bValue == null) return -1;

      if (typeof aValue === "string" && typeof bValue === "string") {
        return direction === "asc"
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      }

      if (aValue < bValue) return direction === "asc" ? -1 : 1;
      if (aValue > bValue) return direction === "asc" ? 1 : -1;
      return 0;
    });

    setSortConfigCan({ key, direction });
    setCandidates(sortedCandidates);
  };

  const handleVacancyClick = (vacancy: Vacancy) => {
    setSelectedVacancy(vacancy);
    setPage(1);
    setSortConfigCan({
      key: null,
      direction: null,
    });
    fetchCandidatesById(vacancy.vacancy_id);
  };

  const filteredCandidates = candidates?.filter((candidate) =>
    candidate.candidate_data.email?.toLowerCase().includes(search.toLowerCase())
  );

  const paginatedCandidates = filteredCandidates?.slice(
    (page - 1) * limit,
    page * limit
  );

  const filteredVacancy = (vacancies ?? []).filter((vacancy) =>
    vacancy.refno.toLowerCase().includes(vacancySearch.toLowerCase())
  );

  const fetchResume = async (candidate: Candidate) => {
    try {
      setLoading(true);
      setIsResumeModalOpen(true);
      const response = await trackedFetch(
        isVacancyFetchFromFiles
          ? `/api/vacancies/files/${candidate.candidate_contactid}`
          : `/api/vacancies/resume/${candidate.candidate_contactid}`,
        {},
        { context: "GetResume" }
      );
      if (response.ok) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const data: any = await response.json();
        setSelectedResume(data);
        getAppInsights()?.trackEvent({
          name: "FE_ResumeFetched",
          properties: {
            candidateId: candidate.candidate_contactid,
          },
        });
      } else {
        console.error("Failed to fetch resume:", response.statusText);
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      console.error("Error fetching resume:", error);
      getAppInsights()?.trackException({
        error: new Error("GetResume api with error is " + error),
        severityLevel: 3,
      });
    } finally {
      setLoading(false);
    }
  };

  const startReview = async () => {
    if (!userName) {
      showNotification("Please enter your email address", "error");
      return;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userName)) {
      showNotification("Please enter a valid email address", "error");
      return;
    }
    localStorage.setItem("userName", userName);
    try {
      setReviewLoading(true);
      const requestObj = {
        reviewer: userName,
      };
      const response = await trackedFetch(
        `/api/vacancies/${selectedVacancy?.vacancy_id}/start`,
        {
          method: "POST",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestObj),
        },
        { context: "StartReview" }
      );
      const data: { locked_by: string; reviewer_config: ReviewerConfig } = (
        await response.json()
      )?.data;
      if (selectedVacancy) {
        const newVacancy: Vacancy = {
          ...selectedVacancy,
          locked_by: data.locked_by as string,
          reviewer_config: data.reviewer_config as ReviewerConfig,
        };

        const newVacancyList = vacancies.map((item) =>
          item.vacancy_id === newVacancy?.vacancy_id ? newVacancy : item
        );
        setSelectedVacancy(newVacancy);
        setVacancies(newVacancyList);
      }
      if (data) {
        setReviewLoading(false);
        setStartRating(false);
        showNotification(`Review process started successfully`, "success");
        getAppInsights()?.trackEvent({
          name: "FE_ReviewStarted",
          properties: {
            vacancyId: selectedVacancy?.vacancy_id,
            reviewer: userName,
            context: "StartReview",
          },
        });
      } else {
        throw "Something went wrong";
      }
    } catch (error: any) {
      showNotification(`Failed to start review, please try again`, "error");
      setReviewLoading(false);
      setStartRating(false);
      console.error("Error: ", error);
      getAppInsights()?.trackException({
        error: new Error("StartReview api with error is " + error),
        severityLevel: 3,
      });
    }
  };

  const completeReview = async () => {
    try {
      setReviewLoading(true);
      const requestObj = {
        reviewer: userName,
      };
      const response = await trackedFetch(
        `/api/vacancies/${selectedVacancy?.vacancy_id}/complete`,
        {
          method: "POST",
          headers: {
            Accept: "application/json",
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestObj),
        },
        { context: "CompleteReview" }
      );
      const data: {
        locked_by: string;
        reviewer_config: ReviewerConfig;
        is_locked: boolean;
      } = (await response.json())?.data;
      if (selectedVacancy) {
        const newVacancy: Vacancy = {
          ...selectedVacancy,
          locked_by: data.locked_by as string,
          reviewer_config: data.reviewer_config as ReviewerConfig,
          is_locked: data.is_locked,
        };

        const newVacancyList = vacancies.map((item) =>
          item.vacancy_id === newVacancy?.vacancy_id ? newVacancy : item
        );
        setSelectedVacancy(newVacancy);
        setVacancies(newVacancyList);
      }

      if (data) {
        setCompleteRating(false);
        setReviewLoading(false);
        showNotification(`Review locked successfully`, "success");
        getAppInsights()?.trackEvent({
          name: "FE_ReviewCompleted",
          properties: {
            vacancyId: selectedVacancy?.vacancy_id,
            reviewer: userName,
            context: "CompleteReview",
          },
        });
      } else {
        throw "Something went wrong";
      }
    } catch (error) {
      showNotification(`Failed to submit review, please try again`, "error");
      setReviewLoading(false);
      console.error("Error: ", error);
      getAppInsights()?.trackException({
        error: new Error("CompleteReview api with error is " + error),
        severityLevel: 3,
      });
    }
  };

  let isReadOnly =
    IS_LOCK_FEATURE_DISABLED === "true" || selectedVacancy?.is_locked;

  const isMercuryReadOnly = false;

  return (
    <NotificationProvider>
      <NotificationList />
      <div className="p-4">
        {isParsedVacancyPopupOpen && (
          <VacancyDetails
            vacancy={selectedVacancy}
            setActiveVacancy={() => {
              setActiveVacancy(null);
              setIsParsedVacancyPopupOpen(false);
            }}
            mercuryPortal={mercuryPortal ?? false}
          />
        )}
        {(vacancyid || mercuryPortal) && (
          <h2 className="text-2xl font-bold text-right py-2">{name}</h2>
        )}
        {!vacancyid && !mercuryPortal ? (
          <h2 className="text-2xl font-bold text-center py-2">
            Vacancies Portal
          </h2>
        ) : (
          selectedVacancy && (
            <>
              <div className="flex justify-center items-baseline">
                <h2 className="text-2xl font-bold text-center">
                  Catalyst Match for the Vacancy
                </h2>
                <span className="ml-2">
                  <a
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      openParsedVacancyPopup();
                    }}
                    className="text-blue-600 underline hover:text-blue-800 cursor-pointer"
                  >
                    [Parsed Vacancy]
                  </a>
                </span>
              </div>
            </>
          )
        )}

        <div className="flex">
          {/* Vacancy Panel */}
          {!vacancyid && !mercuryPortal && (
            <div className="w-[350px] p-2 border-r z-30">
              <h3
                className="text-lg font-semibold mb-2 cursor-pointer"
                onClick={() => handleSort()}
              >
                Vacancy List{" "}
                <ArrowUpDown className="text-black inline ml-1 h-4 w-4" />
              </h3>
              <p>
                <Input
                  type="search"
                  placeholder="Search by vacancy code"
                  value={vacancySearch}
                  onChange={(e) => setVacancySearch(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg p-2 my-2"
                />
              </p>
              {loading && !filteredVacancy.length ? (
                <Loading height="h-[50vh]" />
              ) : (
                <ul className="max-h-[66vh] overflow-y-auto">
                  {filteredVacancy.map((vacancy) => (
                    <VacancyItem
                      vacancy={vacancy}
                      handleVacancyClick={handleVacancyClick}
                      selectedVacancy={selectedVacancy}
                      setSearch={setSearch}
                      key={vacancy.refno}
                      activeVacancy={activeVacancy}
                      setActiveVacancy={setActiveVacancy}
                      startRating={startRating}
                      setStartRating={setStartRating}
                      completeRating={completeRating}
                      setCompleteRating={setCompleteRating}
                      reviewLoading={reviewLoading}
                      userName={userName}
                      setUserName={setUserName}
                      startReview={startReview}
                      completeReview={completeReview}
                    />
                  ))}
                </ul>
              )}
            </div>
          )}
          {/* Candidates Table */}
          <div className="flex-1 px-4">
            {loading && !selectedVacancy ? (
              <div className="flex items-center justify-center h-[50vh]">
                <Loading height="h-[50vh]" />
              </div>
            ) : selectedVacancy && !vacancyid && candidates ? (
              <>
                <div className="flex justify-between items-center">
                  {selectedVacancy && (
                    <h3 className="text-lg font-semibold mb-2">
                      &quot;Candidates for {selectedVacancy.refno}&quot;
                    </h3>
                  )}
                  {isReadOnly && (
                    <div className="bg-blue-200 px-2 py-1 mb-1 rounded-md flex items-center gap-2 mr-2">
                      <LockKeyhole size={15} />
                      <h6 className="text-sm">The below table is read-only</h6>
                    </div>
                  )}
                </div>
                <div className="border border-gray-800 mt-2 rounded-lg">
                  <CandidateTable
                    handleCandidateSort={handleCandidateSort}
                    isResumeModalOpen={isResumeModalOpen}
                    loading={loading}
                    paginatedCandidates={paginatedCandidates}
                    selectedVacancy={selectedVacancy}
                    candidates={candidates}
                    setCandidates={setCandidates}
                    vacancyCandidates={vacancyCandidates}
                    setVacancyCandidates={setVacancyCandidates}
                    fetchResume={fetchResume}
                    mercuryPortal={mercuryPortal ?? false}
                    IS_LOCK_FEATURE_DISABLED={IS_LOCK_FEATURE_DISABLED}
                    fetchCandidatesById={fetchCandidatesById}
                    emailId={emailId ?? ""}
                  />
                </div>
                {/* Pagination */}
                <div className="flex justify-between items-center mt-4">
                  <Select
                    value={limit as unknown as string}
                    onValueChange={(val: string) => {
                      setLimit(Number(val));
                      setPage(1);
                    }}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Select per page-" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        {[50, 100, 150, 200, 500].map((size) => (
                          <SelectItem
                            key={size}
                            value={size as unknown as string}
                          >
                            {size} per page
                          </SelectItem>
                        ))}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  <div>
                    <Button
                      variant={"outline"}
                      onClick={() => setPage((p) => Math.max(p - 1, 1))}
                      disabled={page === 1}
                      className="px-2 py-0.5 mr-1"
                    >
                      <ChevronLeft />
                    </Button>
                    <Button
                      variant={"outline"}
                      onClick={() =>
                        setPage((p) =>
                          p * limit < filteredCandidates.length ? p + 1 : p
                        )
                      }
                      className="px-2 py-0.5"
                      disabled={
                        Math.ceil(filteredCandidates.length / limit) === page
                      }
                    >
                      <ChevronRight />
                    </Button>
                  </div>
                </div>
              </>
            ) : selectedVacancy && vacancyid ? (
              <div className="p-2">
                {SHOW_SEARCH_BY_EMAIL && (
                  <Input
                    type="search"
                    placeholder="Search by email"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="w-1/3 border border-gray-300 rounded-lg p-2 my-2"
                  />
                )}
                <div className="flex gap-2 mt-2 mb-2">
                  {/* <Button
                  variant="default"
                  onClick={saveShortlistedCandidates}
                  disabled={!selectedRows.length}
                  className="mb-2"
                >
                  Shortlist
                </Button> */}
                </div>
                {isMercuryReadOnly && (
                  <div className="bg-blue-200 px-2 py-1 mb-1 rounded-md flex justify-between items-center">
                    <div className="flex items-center gap-2">
                      <LockKeyhole size={15} />
                      <h6 className="text-sm">The below table is read-only</h6>
                    </div>
                    <Badge variant="outline" className="bg-blue-200">
                      Read only
                    </Badge>
                  </div>
                )}
                <div className="border border-gray-800 rounded-lg">
                  <CandidateTable
                    handleCandidateSort={handleCandidateSort}
                    isResumeModalOpen={isResumeModalOpen}
                    loading={loading}
                    paginatedCandidates={paginatedCandidates}
                    selectedVacancy={selectedVacancy}
                    candidates={candidates}
                    setCandidates={setCandidates}
                    vacancyCandidates={vacancyCandidates}
                    setVacancyCandidates={setVacancyCandidates}
                    fetchResume={fetchResume}
                    mercuryPortal={mercuryPortal ?? false}
                    IS_LOCK_FEATURE_DISABLED={IS_LOCK_FEATURE_DISABLED}
                    fetchCandidatesById={fetchCandidatesById}
                    emailId={emailId ?? ""}
                  />
                </div>
                {/* Pagination */}
                <div className="flex justify-between items-center mt-4">
                  <Select
                    value={limit as unknown as string}
                    onValueChange={(val: string) => {
                      setLimit(Number(val));
                      setPage(1);
                    }}
                  >
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="Select per page-" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        {[50, 100, 150, 200, 500].map((size) => (
                          <SelectItem
                            key={size}
                            value={size as unknown as string}
                          >
                            {size} per page
                          </SelectItem>
                        ))}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                  <div>
                    <Button
                      variant={"outline"}
                      onClick={() => setPage((p) => Math.max(p - 1, 1))}
                      disabled={page === 1}
                      className="px-2 py-0.5 mr-1"
                    >
                      <ChevronLeft />
                    </Button>
                    <Button
                      variant={"outline"}
                      onClick={() =>
                        setPage((p) =>
                          p * limit < filteredCandidates.length ? p + 1 : p
                        )
                      }
                      className="px-2 py-0.5"
                      disabled={
                        Math.ceil(filteredCandidates.length / limit) === page
                      }
                    >
                      <ChevronRight />
                    </Button>
                  </div>
                </div>
              </div>
            ) : candidates === undefined && mercuryPortal ? (
              <div className="m-auto w-full max-w-md p-4 bg-red-100 border border-red-400 rounded-lg">
                <p className="text-red-500 text-center">
                  Vacancy Id parameter not passed in by Mercury. Contact support
                  if this persists.
                </p>
              </div>
            ) : (
              candidates?.length === 0 && (
                <div className="relative flex items-center justify-end h-[50vh] flex-col">
                  <p className="text-3xl text-gray-700 mb-4">
                    No candidates found
                  </p>
                  <Image src={noCandidatesImage} alt="no-candidates-image" />
                  <p className="text-gray-500 text-sm absolute bottom-2 italic">
                    (No Matching Vacancy Available parameter passed. Contact
                    support if this persists.)
                  </p>
                </div>
              )
            )}
          </div>
        </div>
        <CandidateResume
          vacancy={selectedVacancy}
          selectedResume={selectedResume}
          setSelectedResume={setSelectedResume}
          isResumeModalOpen={isResumeModalOpen}
          setIsResumeModalOpen={setIsResumeModalOpen}
          isLoading={loading}
        />
        {vacancyid === undefined &&
          !loading &&
          pageNotFound &&
          mercuryPortal && (
            <div className="bg-white border-t p-4 h-screen flex items-center justify-center">
              <p>
                <span className="text-lg mr-1">404</span>Page not found
              </p>
            </div>
          )}
      </div>
    </NotificationProvider>
  );
};

export default Candidates;
