const CHUNK_PUBLIC_PATH = "server/app/api/pools/stats/route.js";
const runtime = require("../../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_next_ddd516._.js");
runtime.loadChunk("server/chunks/node_modules_next-auth_35e54c._.js");
runtime.loadChunk("server/chunks/node_modules_openid-client_ef38b3._.js");
runtime.loadChunk("server/chunks/node_modules_jose_dist_node_cjs_b4a801._.js");
runtime.loadChunk("server/chunks/node_modules_@microsoft_applicationinsights-core-js_dist-es5_4fbf1b._.js");
runtime.loadChunk("server/chunks/node_modules_@microsoft_applicationinsights-common_dist-es5_0d9150._.js");
runtime.loadChunk("server/chunks/node_modules_@microsoft_applicationinsights-analytics-js_dist-es5_ea98a7._.js");
runtime.loadChunk("server/chunks/node_modules_@microsoft_applicationinsights-dependencies-js_dist-es5_3e5946._.js");
runtime.loadChunk("server/chunks/node_modules_@microsoft_applicationinsights-channel-js_dist-es5_a6f2cc._.js");
runtime.loadChunk("server/chunks/node_modules_d441ab._.js");
runtime.loadChunk("server/chunks/[root of the server]__a2f6bf._.js");
runtime.loadChunk("server/chunks/_c0c1f0._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/pools/stats/route/actions.js [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/api/pools/stats/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
