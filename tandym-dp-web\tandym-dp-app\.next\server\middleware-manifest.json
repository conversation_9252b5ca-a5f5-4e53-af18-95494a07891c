{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_bbcfe3._.js", "server/edge/chunks/[root of the server]__c55477._.js", "server/edge/chunks/edge-wrapper_aaa207.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/candidates(\\\\.json)?[\\/#\\?]?$", "originalSource": "/candidates"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/jobs(\\\\.json)?[\\/#\\?]?$", "originalSource": "/jobs"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/skills-editor(\\\\.json)?[\\/#\\?]?$", "originalSource": "/skills-editor"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/workforce-index(\\\\.json)?[\\/#\\?]?$", "originalSource": "/workforce-index"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/vacancy(\\\\.json)?[\\/#\\?]?$", "originalSource": "/vacancy"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/vacancy(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/vacancy/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/experiments(\\\\.json)?[\\/#\\?]?$", "originalSource": "/experiments"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/CandidateTuning\\/For_Mercury_Portal(\\\\.json)?[\\/#\\?]?$", "originalSource": "/CandidateTuning/For_Mercury_Portal"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "WHLvqhg+5uAd2CK00BvwlNoi+/rxpBSUqGf1awguCRA=", "__NEXT_PREVIEW_MODE_ID": "a71717a6ad2e3f1d5b2c4d8eed18f082", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f3d9b8f1a1e9fcc13fab8dacf23367f2a489df0b5aa43d268f6e23fb48ff2c8e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ed680ec08c98bb6eb302a0c259f77d202b002f68c9f9bef9b20c61a861e0479b"}}}, "sortedMiddleware": ["/"], "functions": {}}