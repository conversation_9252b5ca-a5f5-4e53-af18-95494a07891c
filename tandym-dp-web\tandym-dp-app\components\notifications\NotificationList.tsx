"use client";
import { useNotification } from "@/hooks/useNotification";
import { X } from "lucide-react";

export default function NotificationList() {
  const { notifications, removeNotification } = useNotification();

  return (
    <div className="fixed top-4 right-4 z-[9999] space-y-2 w-[300px]">
      {notifications.map((notif) => (
        <div
          key={notif.id}
          className={`flex items-start justify-between gap-2 px-4 py-3 rounded shadow-md text-white animate-slide-in-down
            ${
              notif.type === "success"
                ? "bg-green-600"
                : notif.type === "error"
                ? "bg-red-600"
                : notif.type === "warning"
                ? "bg-yellow-500"
                : "bg-blue-600"
            }
          `}
        >
          <div className="text-sm flex-1">{notif.message}</div>
          <button
            onClick={() => removeNotification(notif.id)}
            className="ml-2 mt-1"
          >
            <X className="w-4 h-4 text-white" />
          </button>
        </div>
      ))}
    </div>
  );
}
