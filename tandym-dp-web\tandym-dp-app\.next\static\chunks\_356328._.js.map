{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/hooks/useNotification.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useState, createContext, useContext, ReactNode } from \"react\";\r\n\r\ninterface Notification {\r\n  id: number;\r\n  message: string;\r\n  type: \"success\" | \"error\" | \"info\" | \"warning\";\r\n}\r\n\r\ninterface NotificationContextType {\r\n  notifications: Notification[];\r\n  showNotification: (message: string, type?: Notification[\"type\"]) => void;\r\n  removeNotification: (id: number) => void;\r\n}\r\n\r\nconst NotificationContext = createContext<NotificationContextType | null>(null);\r\n\r\nexport const NotificationProvider = ({ children }: { children: ReactNode }) => {\r\n  const [notifications, setNotifications] = useState<Notification[]>([]);\r\n\r\n  const showNotification = (\r\n    message: string,\r\n    type: Notification[\"type\"] = \"info\"\r\n  ) => {\r\n    const id = Date.now();\r\n    setNotifications((prev) => [...prev, { id, message, type }]);\r\n\r\n    // Auto-remove after 3 seconds\r\n    setTimeout(() => removeNotification(id), 3000);\r\n  };\r\n\r\n  const removeNotification = (id: number) => {\r\n    setNotifications((prev) => prev.filter((notif) => notif.id !== id));\r\n  };\r\n\r\n  return (\r\n    <NotificationContext.Provider\r\n      value={{ notifications, showNotification, removeNotification }}\r\n    >\r\n      {children}\r\n    </NotificationContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useNotification = () => {\r\n  const context = useContext(NotificationContext);\r\n  if (!context) {\r\n    throw new Error(\r\n      \"useNotification must be used within a NotificationProvider\"\r\n    );\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;;;AADA;;AAeA,MAAM,oCAAsB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAkC;AAEnE,MAAM,uBAAuB,CAAC,EAAE,QAAQ,EAA2B;;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,MAAM,mBAAmB,CACvB,SACA,OAA6B,MAAM;QAEnC,MAAM,KAAK,KAAK,GAAG;QACnB,iBAAiB,CAAC,OAAS;mBAAI;gBAAM;oBAAE;oBAAI;oBAAS;gBAAK;aAAE;QAE3D,8BAA8B;QAC9B,WAAW,IAAM,mBAAmB,KAAK;IAC3C;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAC,OAAS,KAAK,MAAM,CAAC,CAAC,QAAU,MAAM,EAAE,KAAK;IACjE;IAEA,qBACE,6LAAC,oBAAoB,QAAQ;QAC3B,OAAO;YAAE;YAAe;YAAkB;QAAmB;kBAE5D;;;;;;AAGP;GAzBa;KAAA;AA2BN,MAAM,kBAAkB;;IAC7B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MACR;IAEJ;IACA,OAAO;AACT;IARa"}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/NotificationBar.tsx"], "sourcesContent": ["import { useNotification } from \"../hooks/useNotification\";\r\n\r\nconst NotificationBar = () => {\r\n  const { notifications, removeNotification } = useNotification();\r\n\r\n  return (\r\n    <div className=\"fixed top-4 right-4 z-50 flex flex-col gap-2\">\r\n      {notifications.map(({ id, message, type }) => (\r\n        <div\r\n          key={id}\r\n          className={`flex items-center px-4 py-2 text-white rounded-lg shadow-md transition-all \r\n            ${type === \"success\" ? \"bg-green-500\" : \"\"}\r\n            ${type === \"error\" ? \"bg-red-500\" : \"\"}\r\n            ${type === \"warning\" ? \"bg-yellow-500\" : \"\"}\r\n            ${type === \"info\" ? \"bg-blue-500\" : \"\"}`}\r\n        >\r\n          <span className=\"flex-1\">{message}</span>\r\n          <button\r\n            className=\"ml-4 text-white font-bold\"\r\n            onClick={() => removeNotification(id)}\r\n          >\r\n            ✖\r\n          </button>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default NotificationBar;\r\n"], "names": [], "mappings": ";;;;AAAA;;;;AAEA,MAAM,kBAAkB;;IACtB,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAE5D,qBACE,6LAAC;QAAI,WAAU;kBACZ,cAAc,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,iBACvC,6LAAC;gBAEC,WAAW,CAAC;YACV,EAAE,SAAS,YAAY,iBAAiB,GAAG;YAC3C,EAAE,SAAS,UAAU,eAAe,GAAG;YACvC,EAAE,SAAS,YAAY,kBAAkB,GAAG;YAC5C,EAAE,SAAS,SAAS,gBAAgB,IAAI;;kCAE1C,6LAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,mBAAmB;kCACnC;;;;;;;eAXI;;;;;;;;;;AAkBf;GAzBM;;QAC0C,4HAAA,CAAA,kBAAe;;;KADzD;uCA2BS"}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 139, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/context/SkillsContext.tsx"], "sourcesContent": ["\"use client\";\r\nimport {\r\n  createContext,\r\n  useState,\r\n  useContext,\r\n  ReactNode,\r\n  SetStateAction,\r\n  Dispatch,\r\n} from \"react\";\r\n\r\n// Define types\r\ninterface Skill {\r\n  id: number;\r\n  name: string;\r\n  weight: string;\r\n  subcategory_id: number;\r\n  attribute_type_id: number;\r\n  created_at: string;\r\n  updated_at: string;\r\n  is_approved: boolean;\r\n}\r\n\r\ninterface SubCategory {\r\n  id: number;\r\n  name: string;\r\n  category_id: number;\r\n  category_name: string;\r\n}\r\n\r\ninterface Weight {\r\n  id: number;\r\n  subcategory_id: number;\r\n  attribute_type_id: number;\r\n  weight_level: string;\r\n  weight_value: number;\r\n}\r\n\r\ninterface SkillsContextType {\r\n  skillsData: SubCategory[];\r\n  setSkillsData: Dispatch<SetStateAction<SubCategory[]>>;\r\n  selectedTab: string;\r\n  setSelectedTab: Dispatch<SetStateAction<string>>;\r\n  singleSkillData: Skill[] | undefined;\r\n  setSingleSkillData: Dispatch<SetStateAction<Skill[] | undefined>>;\r\n  selectedCategory: SubCategory | undefined;\r\n  setSelectedCategory: Dispatch<SetStateAction<SubCategory | undefined>>;\r\n  sendUpdatedData: any;\r\n  setSendUpdatedData: Dispatch<SetStateAction<any>>;\r\n  trackCurrentData: any;\r\n  setTrackCurrentData: Dispatch<SetStateAction<any>>;\r\n  loading: boolean;\r\n  setLoading: Dispatch<SetStateAction<boolean>>;\r\n  loadingPost: boolean;\r\n  setLoadingPost: Dispatch<SetStateAction<boolean>>;\r\n  loadingDelete: boolean;\r\n  setLoadingDelete: Dispatch<SetStateAction<boolean>>;\r\n  loadingApprovedStatus: boolean;\r\n  setLoadingApprovedStatus: Dispatch<SetStateAction<boolean>>;\r\n  isDeleteOpen: string;\r\n  setDeleteOpen: Dispatch<SetStateAction<string>>;\r\n  isAllDeleteOpen: boolean;\r\n  setAllDeleteOpen: Dispatch<SetStateAction<boolean>>;\r\n  selectedRows: Set<number>;\r\n  setSelectedRows: Dispatch<SetStateAction<Set<number>>>;\r\n  weightsOfSubcategory: Weight[];\r\n  setWeightsOfSubcategory: Dispatch<SetStateAction<Weight[]>>;\r\n  sortColumn: string | null;\r\n  setSortColumn: Dispatch<SetStateAction<string | null>>;\r\n  sortOrder: \"asc\" | \"desc\";\r\n  setSortOrder: Dispatch<SetStateAction<\"asc\" | \"desc\">>;\r\n  isOpenDiscardModal: boolean;\r\n  setIsOpenDiscardModal: Dispatch<SetStateAction<boolean>>;\r\n  updatedAction: any;\r\n  setUpdatedAction: Dispatch<SetStateAction<any>>;\r\n  selecteApproveddRows: any;\r\n  setSelectedApproveddRows: Dispatch<SetStateAction<any>>;\r\n  isHeaderTabChange: boolean;\r\n  setIsHeaderTabChange: Dispatch<SetStateAction<boolean>>;\r\n}\r\n\r\n// Create Context\r\nconst SkillsContext = createContext<SkillsContextType | undefined>(undefined);\r\n\r\n// Provider Component\r\nexport const SkillsProvider = ({ children }: { children: ReactNode }) => {\r\n  const [skillsData, setSkillsData] = useState<SubCategory[]>([]);\r\n  const [selectedTab, setSelectedTab] = useState<string>(\"Soft Skills\");\r\n  const [singleSkillData, setSingleSkillData] = useState<Skill[] | undefined>();\r\n  const [selectedCategory, setSelectedCategory] = useState<SubCategory>();\r\n  const [sendUpdatedData, setSendUpdatedData] = useState<any>({});\r\n  const [updatedAction, setUpdatedAction] = useState<any>({});\r\n  const [trackCurrentData, setTrackCurrentData] = useState<any>({});\r\n  const [loading, setLoading] = useState(false);\r\n  const [loadingPost, setLoadingPost] = useState(false);\r\n  const [loadingDelete, setLoadingDelete] = useState(false);\r\n  const [loadingApprovedStatus, setLoadingApprovedStatus] = useState(false);\r\n  const [isDeleteOpen, setDeleteOpen] = useState(\"\");\r\n  const [isAllDeleteOpen, setAllDeleteOpen] = useState(false);\r\n  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set());\r\n  const [selecteApproveddRows, setSelectedApproveddRows] = useState<any>({});\r\n  const [weightsOfSubcategory, setWeightsOfSubcategory] = useState<Weight[]>(\r\n    []\r\n  );\r\n  const [sortColumn, setSortColumn] = useState<string | null>(null);\r\n  const [sortOrder, setSortOrder] = useState<\"asc\" | \"desc\">(\"asc\");\r\n  const [isOpenDiscardModal, setIsOpenDiscardModal] = useState(false);\r\n  const [isHeaderTabChange, setIsHeaderTabChange] = useState(false);\r\n\r\n  return (\r\n    <SkillsContext.Provider\r\n      value={{\r\n        skillsData,\r\n        setSkillsData,\r\n        selectedTab,\r\n        setSelectedTab,\r\n        singleSkillData,\r\n        setSingleSkillData,\r\n        selectedCategory,\r\n        setSelectedCategory,\r\n        sendUpdatedData,\r\n        setSendUpdatedData,\r\n        trackCurrentData,\r\n        setTrackCurrentData,\r\n        loading,\r\n        setLoading,\r\n        loadingPost,\r\n        setLoadingPost,\r\n        loadingDelete,\r\n        setLoadingDelete,\r\n        isDeleteOpen,\r\n        setDeleteOpen,\r\n        isAllDeleteOpen,\r\n        setAllDeleteOpen,\r\n        selectedRows,\r\n        setSelectedRows,\r\n        weightsOfSubcategory,\r\n        setWeightsOfSubcategory,\r\n        sortColumn,\r\n        setSortColumn,\r\n        sortOrder,\r\n        setSortOrder,\r\n        isOpenDiscardModal,\r\n        setIsOpenDiscardModal,\r\n        updatedAction,\r\n        setUpdatedAction,\r\n        selecteApproveddRows,\r\n        setSelectedApproveddRows,\r\n        loadingApprovedStatus,\r\n        setLoadingApprovedStatus,\r\n        isHeaderTabChange,\r\n        setIsHeaderTabChange,\r\n      }}\r\n    >\r\n      {children}\r\n    </SkillsContext.Provider>\r\n  );\r\n};\r\n\r\n// Hook to use SkillsContext\r\nexport const useSkills = () => {\r\n  const context = useContext(SkillsContext);\r\n  if (!context) {\r\n    throw new Error(\"useSkills must be used within a SkillsProvider\");\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;;;AADA;;AAgFA,iBAAiB;AACjB,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAiC;AAG5D,MAAM,iBAAiB,CAAC,EAAE,QAAQ,EAA2B;;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC9D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACvD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAC7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,cAAc,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAClE,MAAM,CAAC,sBAAsB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO,CAAC;IACxE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC7D,EAAE;IAEJ,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC5D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,qBACE,6LAAC,cAAc,QAAQ;QACrB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;GAxEa;KAAA;AA2EN,MAAM,YAAY;;IACvB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa"}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/providers.tsx"], "sourcesContent": ["// src/app/providers.tsx\r\n'use client';\r\n\r\nimport { SessionProvider } from 'next-auth/react';\r\n\r\nexport function Providers({ children }: { children: React.ReactNode }) {\r\n  return <SessionProvider>{children}</SessionProvider>;\r\n}\r\n"], "names": [], "mappings": "AAAA,wBAAwB;;;;;AAGxB;AAFA;;;AAIO,SAAS,UAAU,EAAE,QAAQ,EAAiC;IACnE,qBAAO,6LAAC,iJAAA,CAAA,kBAAe;kBAAE;;;;;;AAC3B;KAFgB"}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/utils/tabRoutes.ts"], "sourcesContent": ["// utils/tabRoutes.ts\r\n\r\nexport enum TAB_ROUTE_MAP {\r\n  home = \"/\",\r\n  Vacancy = \"/candidates\",\r\n  jobs = \"/jobs\",\r\n  skillsEditor = \"/skills-editor\",\r\n  workforceIndex = \"/workforce-index\",\r\n  // subcategoryConfig = \"/subcategory-config\",\r\n}\r\n\r\nexport interface TabRoute {\r\n  route: string;\r\n  show?: boolean;\r\n}\r\n\r\nexport const getTabMenus = (\r\n  entitlements?: Record<string, boolean>,\r\n  isADlogin?: boolean\r\n): TabRoute[] => {\r\n  return [\r\n    { route: TAB_ROUTE_MAP.home },\r\n    {\r\n      route: TAB_ROUTE_MAP.skillsEditor,\r\n      show: isADlogin ? entitlements?.Sub_Catregory : true,\r\n    },\r\n    {\r\n      route: TAB_ROUTE_MAP.workforceIndex,\r\n      show: isADlogin ? entitlements?.Work_force_Index : true,\r\n    },\r\n    {\r\n      route: TAB_ROUTE_MAP.Vacancy,\r\n      show: isADlogin ? entitlements?.Vacancy : true,\r\n    },\r\n    // {\r\n    //   route: TAB_ROUTE_MAP.subcategoryConfig,\r\n    //   show: isADlogin ? entitlements?.Sc_Score_Config : true,\r\n    // },\r\n  ].filter((tab) => tab.show !== false);\r\n};\r\n"], "names": [], "mappings": "AAAA,qBAAqB;;;;;AAEd,IAAA,AAAK,uCAAA;;;;;;WAAA;;AAcL,MAAM,cAAc,CACzB,cACA;IAEA,OAAO;QACL;YAAE,KAAK;QAAqB;QAC5B;YACE,KAAK;YACL,MAAM,YAAY,cAAc,gBAAgB;QAClD;QACA;YACE,KAAK;YACL,MAAM,YAAY,cAAc,mBAAmB;QACrD;QACA;YACE,KAAK;YACL,MAAM,YAAY,cAAc,UAAU;QAC5C;KAKD,CAAC,MAAM,CAAC,CAAC,MAAQ,IAAI,IAAI,KAAK;AACjC"}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 316, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/app/actions/getAppInsightsConnectionString.ts"], "sourcesContent": ["\"use server\";\r\n\r\nexport async function getAppInsightsConnectionString() {\r\n  return process.env.NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING || \"\";\r\n}\r\n"], "names": [], "mappings": ";;;;;IAEsB"}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/appInsights.ts"], "sourcesContent": ["import { getAppInsightsConnectionString } from \"@/app/actions/getAppInsightsConnectionString\";\r\nimport { ApplicationInsights } from \"@microsoft/applicationinsights-web\";\r\n\r\nlet appInsights: ApplicationInsights | null = null;\r\n\r\nexport async function initAppInsights() {\r\n  const appInsightsConnectionString = await getAppInsightsConnectionString();\r\n\r\n  if (!appInsights) {\r\n    const connectionString =\r\n      \"InstrumentationKey=\" + appInsightsConnectionString || \"\";\r\n    appInsights = new ApplicationInsights({\r\n      config: {\r\n        connectionString,\r\n        enableAutoRouteTracking: false, // We'll do this manually\r\n      },\r\n    });\r\n    appInsights.loadAppInsights();\r\n  }\r\n  return appInsights;\r\n}\r\n\r\nexport function getAppInsights() {\r\n  return appInsights;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,IAAI,cAA0C;AAEvC,eAAe;IACpB,MAAM,8BAA8B,MAAM,CAAA,GAAA,mJAAA,CAAA,iCAA8B,AAAD;IAEvE,IAAI,CAAC,aAAa;QAChB,MAAM,mBACJ,wBAAwB,+BAA+B;QACzD,cAAc,IAAI,6OAAA,CAAA,sBAAmB,CAAC;YACpC,QAAQ;gBACN;gBACA,yBAAyB;YAC3B;QACF;QACA,YAAY,eAAe;IAC7B;IACA,OAAO;AACT;AAEO,SAAS;IACd,OAAO;AACT"}}, {"offset": {"line": 357, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const USER_UUID_KEY = \"tg-user-uuid\";\r\n\r\nexport function getOrCreateUserUuid(): string {\r\n  if (typeof window === \"undefined\") return \"\";\r\n  let id = localStorage.getItem(USER_UUID_KEY);\r\n  if (!id) {\r\n    id = crypto.randomUUID();\r\n    localStorage.setItem(USER_UUID_KEY, id);\r\n  }\r\n  return id;\r\n}\r\n\r\nexport function clearUserUuid() {\r\n  localStorage.removeItem(USER_UUID_KEY); // call on logout if desired\r\n}\r\nexport enum APPLICATION_NAVIGATION_ROUTES {\r\n  VACANCY = \"Vacancy\",\r\n  WORK_FORCE_INDEX = \"Work_force_Index\",\r\n  SUB_CATEGORY = \"Sub_Catregory\",\r\n  SEARCH_MATCH = \"Search_Match\",\r\n  SC_SCORE_CONFIG = \"Sc_Score_Config\",\r\n}\r\nexport const emailInternalAddress = \"<EMAIL>\";\r\n\r\nexport enum FEATURE_NAMES {\r\n  SORTLIST_CANDIDATE_FEATURE = \"Shortlist Candidate Feature\",\r\n  THUMB_REVIEW_FEATURE = \"Thumb Review Feature\",\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,gBAAgB;AAEtB,SAAS;IACd,uCAAmC;;IAAS;IAC5C,IAAI,KAAK,aAAa,OAAO,CAAC;IAC9B,IAAI,CAAC,IAAI;QACP,KAAK,OAAO,UAAU;QACtB,aAAa,OAAO,CAAC,eAAe;IACtC;IACA,OAAO;AACT;AAEO,SAAS;IACd,aAAa,UAAU,CAAC,gBAAgB,4BAA4B;AACtE;AACO,IAAA,AAAK,uDAAA;;;;;;WAAA;;AAOL,MAAM,uBAAuB;AAE7B,IAAA,AAAK,uCAAA;;;WAAA"}}, {"offset": {"line": 411, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/trackApi.ts"], "sourcesContent": ["// lib/trackedFetch.ts\r\nimport { getAppInsights } from \"./appInsights\";\r\nimport { getOrCreateUserUuid } from \"./utils\";\r\n\r\nexport async function trackedFetch(\r\n  input: RequestInfo | URL,\r\n  init: RequestInit = {},\r\n  extraCtx: Record<string, any> = {}\r\n): Promise<Response> {\r\n  const uuid = getOrCreateUserUuid();\r\n\r\n  // --- inject header so the backend can log/link this request too\r\n  const headers = new Headers(init.headers);\r\n  headers.set(\"X-User-UUID\", uuid);\r\n\r\n  const start = performance.now();\r\n  try {\r\n    const response = await fetch(input, { ...init, headers });\r\n    const dur = performance.now() - start;\r\n\r\n    getAppInsights()?.trackDependencyData({\r\n      id: crypto.randomUUID(), // per-call correlation id\r\n      name: typeof input === \"string\" ? input : input.toString(),\r\n      target: window.location.hostname,\r\n      duration: dur,\r\n      success: response.ok,\r\n      responseCode: response.status,\r\n      type: \"Fetch\",\r\n      properties: { userUuid: uuid, ...extraCtx },\r\n    });\r\n\r\n    if (!response.ok) throw new Error(`HTTP ${response.status}`);\r\n    return response;\r\n  } catch (err) {\r\n    const dur = performance.now() - start;\r\n    getAppInsights()?.trackDependencyData({\r\n      id: crypto.randomUUID(),\r\n      name: typeof input === \"string\" ? input : input.toString(),\r\n      target: window.location.hostname,\r\n      duration: dur,\r\n      success: false,\r\n      responseCode: 0,\r\n      type: \"Fetch\",\r\n      properties: {\r\n        userUuid: uuid,\r\n        error: err instanceof Error ? err.message : String(err),\r\n        ...extraCtx,\r\n      },\r\n    });\r\n    getAppInsights()?.trackException({\r\n      error: err as Error,\r\n      properties: { userUuid: uuid },\r\n    });\r\n    throw err;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AACtB;AACA;;;AAEO,eAAe,aACpB,KAAwB,EACxB,OAAoB,CAAC,CAAC,EACtB,WAAgC,CAAC,CAAC;IAElC,MAAM,OAAO,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD;IAE/B,iEAAiE;IACjE,MAAM,UAAU,IAAI,QAAQ,KAAK,OAAO;IACxC,QAAQ,GAAG,CAAC,eAAe;IAE3B,MAAM,QAAQ,YAAY,GAAG;IAC7B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,OAAO;YAAE,GAAG,IAAI;YAAE;QAAQ;QACvD,MAAM,MAAM,YAAY,GAAG,KAAK;QAEhC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,oBAAoB;YACpC,IAAI,OAAO,UAAU;YACrB,MAAM,OAAO,UAAU,WAAW,QAAQ,MAAM,QAAQ;YACxD,QAAQ,OAAO,QAAQ,CAAC,QAAQ;YAChC,UAAU;YACV,SAAS,SAAS,EAAE;YACpB,cAAc,SAAS,MAAM;YAC7B,MAAM;YACN,YAAY;gBAAE,UAAU;gBAAM,GAAG,QAAQ;YAAC;QAC5C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;QAC3D,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,MAAM,MAAM,YAAY,GAAG,KAAK;QAChC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,oBAAoB;YACpC,IAAI,OAAO,UAAU;YACrB,MAAM,OAAO,UAAU,WAAW,QAAQ,MAAM,QAAQ;YACxD,QAAQ,OAAO,QAAQ,CAAC,QAAQ;YAChC,UAAU;YACV,SAAS;YACT,cAAc;YACd,MAAM;YACN,YAAY;gBACV,UAAU;gBACV,OAAO,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;gBACnD,GAAG,QAAQ;YACb;QACF;QACA,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;YAC/B,OAAO;YACP,YAAY;gBAAE,UAAU;YAAK;QAC/B;QACA,MAAM;IACR;AACF"}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/context/EntitlementContext.tsx"], "sourcesContent": ["\"use client\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { TAB_ROUTE_MAP } from \"@/utils/tabRoutes\";\r\n\r\nimport React, {\r\n  createContext,\r\n  useContext,\r\n  useState,\r\n  useEffect,\r\n  ReactNode,\r\n  useRef,\r\n} from \"react\";\r\nimport { getAppInsights } from \"@/library/appInsights\";\r\nimport { trackedFetch } from \"@/library/trackApi\";\r\nimport { useSession } from \"next-auth/react\";\r\n\r\n// Map of entitlements: { FeatureA: true, FeatureB: false }\r\nexport interface EntitlementMap {\r\n  [key: string]: boolean;\r\n}\r\n\r\nexport interface EntitlementContextProps {\r\n  entitlements: EntitlementMap;\r\n  setEntitlements: (value: EntitlementMap) => void;\r\n  isLoaded: boolean;\r\n}\r\n\r\nconst EntitlementContext = createContext<EntitlementContextProps | undefined>(\r\n  undefined\r\n);\r\n\r\nexport const EntitlementProvider = ({ children }: { children: ReactNode }) => {\r\n  const pathname = usePathname();\r\n  const routes = Object.values(TAB_ROUTE_MAP) as string[];\r\n  const [entitlements, setEntitlements] = useState<EntitlementMap>({});\r\n  const [isLoaded, setIsLoaded] = useState(false);\r\n  const calledRef = useRef(false);\r\n  const { data: session, status } = useSession();\r\n\r\n  useEffect(() => {\r\n    if (calledRef.current) return;\r\n    if (status !== \"authenticated\") return; // Wait until session is loaded\r\n    let interval: NodeJS.Timeout | null = null;\r\n\r\n    const fetchEntitlementData = async () => {\r\n      try {\r\n        const response = await trackedFetch(\r\n          \"/api/entitlements\",\r\n          {},\r\n          { context: \"Entitlements\" }\r\n        );\r\n        if (!response.ok) throw new Error(\"Network response was not ok\");\r\n\r\n        const data = await response.json();\r\n        console.log(\"email is\", session?.user?.email);\r\n        if (!data.error) {\r\n          setEntitlements(data.entitlement);\r\n          getAppInsights()?.trackEvent({\r\n            name: \"FE_Entitlements_Fetched\",\r\n            properties: {\r\n              email: session?.user?.email || \"\",\r\n            },\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Error fetching entitlement data:\", error);\r\n        getAppInsights()?.trackException({\r\n          error: new Error(\"Entitlements api with error is \" + error),\r\n          severityLevel: 3,\r\n        });\r\n      } finally {\r\n        setIsLoaded(true);\r\n      }\r\n    };\r\n\r\n    const checkAndFetch = () => {\r\n      const ai = getAppInsights();\r\n      if (routes.includes(pathname) && ai && !calledRef.current) {\r\n        calledRef.current = true;\r\n        fetchEntitlementData();\r\n        if (interval) clearInterval(interval);\r\n      }\r\n    };\r\n\r\n    checkAndFetch();\r\n    interval = setInterval(checkAndFetch, 100);\r\n\r\n    return () => {\r\n      if (interval) clearInterval(interval);\r\n    };\r\n  }, [pathname, routes, status]);\r\n\r\n  return (\r\n    <EntitlementContext.Provider\r\n      value={{ entitlements, setEntitlements, isLoaded }}\r\n    >\r\n      {children}\r\n    </EntitlementContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useEntitlement = () => {\r\n  const context = useContext(EntitlementContext);\r\n  if (!context) {\r\n    throw new Error(\r\n      \"useEntitlement must be used within an EntitlementProvider\"\r\n    );\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;AAQA;AACA;AACA;;;AAdA;;;;;;;AA2BA,MAAM,mCAAqB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EACrC;AAGK,MAAM,sBAAsB,CAAC,EAAE,QAAQ,EAA2B;;IACvE,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,OAAO,MAAM,CAAC,qHAAA,CAAA,gBAAa;IAC1C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,CAAC;IAClE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,UAAU,OAAO,EAAE;YACvB,IAAI,WAAW,iBAAiB,QAAQ,+BAA+B;YACvE,IAAI,WAAkC;YAEtC,MAAM;sEAAuB;oBAC3B,IAAI;wBACF,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAChC,qBACA,CAAC,GACD;4BAAE,SAAS;wBAAe;wBAE5B,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM;wBAElC,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,QAAQ,GAAG,CAAC,YAAY,SAAS,MAAM;wBACvC,IAAI,CAAC,KAAK,KAAK,EAAE;4BACf,gBAAgB,KAAK,WAAW;4BAChC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;gCAC3B,MAAM;gCACN,YAAY;oCACV,OAAO,SAAS,MAAM,SAAS;gCACjC;4BACF;wBACF;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,oCAAoC;wBAClD,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;4BAC/B,OAAO,IAAI,MAAM,oCAAoC;4BACrD,eAAe;wBACjB;oBACF,SAAU;wBACR,YAAY;oBACd;gBACF;;YAEA,MAAM;+DAAgB;oBACpB,MAAM,KAAK,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD;oBACxB,IAAI,OAAO,QAAQ,CAAC,aAAa,MAAM,CAAC,UAAU,OAAO,EAAE;wBACzD,UAAU,OAAO,GAAG;wBACpB;wBACA,IAAI,UAAU,cAAc;oBAC9B;gBACF;;YAEA;YACA,WAAW,YAAY,eAAe;YAEtC;iDAAO;oBACL,IAAI,UAAU,cAAc;gBAC9B;;QACF;wCAAG;QAAC;QAAU;QAAQ;KAAO;IAE7B,qBACE,6LAAC,mBAAmB,QAAQ;QAC1B,OAAO;YAAE;YAAc;YAAiB;QAAS;kBAEhD;;;;;;AAGP;GApEa;;QACM,qIAAA,CAAA,cAAW;QAKM,iJAAA,CAAA,aAAU;;;KANjC;AAsEN,MAAM,iBAAiB;;IAC5B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MACR;IAEJ;IACA,OAAO;AACT;IARa"}}, {"offset": {"line": 606, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/components/AppInsights.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { useEffect } from \"react\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { initAppInsights, getAppInsights } from \"@/library/appInsights\";\r\nimport { getOrCreateUserUuid } from \"@/library/utils\";\r\nimport { useSession } from \"next-auth/react\";\r\n\r\nconst AppInsightsClient = () => {\r\n  const pathname = usePathname();\r\n  const { data: session } = useSession();\r\n\r\n  // 1. Initialize App Insights once\r\n  useEffect(() => {\r\n    initAppInsights();\r\n\r\n    window.onerror = (msg, src, line, col, err) => {\r\n      getAppInsights()?.trackException({\r\n        error: err || new Error(String(msg)),\r\n      });\r\n    };\r\n  }, []);\r\n\r\n  // 2. Register telemetry initializer once, but always use latest session/uuid\r\n  useEffect(() => {\r\n    const uuid = getOrCreateUserUuid();\r\n    const ai = getAppInsights();\r\n    if (!ai) return;\r\n\r\n    // Remove previous initializers if needed (optional, depends on SDK)\r\n    ai.addTelemetryInitializer((envelope) => {\r\n      const baseData: any = envelope.data?.baseData ?? {};\r\n      baseData.properties = {\r\n        ...(baseData.properties || {}),\r\n        userUuid: uuid,\r\n        email: session?.user?.email || \"\",\r\n      };\r\n      envelope.data!.baseData = baseData;\r\n    });\r\n\r\n    // Set authenticated user context\r\n    ai.setAuthenticatedUserContext(uuid);\r\n  }, [session]);\r\n\r\n  // 3. Track page views\r\n  useEffect(() => {\r\n    getAppInsights()?.trackPageView({ uri: pathname });\r\n  }, [pathname]);\r\n\r\n  return null;\r\n};\r\n\r\nexport default AppInsightsClient;\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AACA;AACA;;AANA;;;;;;AAQA,MAAM,oBAAoB;;IACxB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAEnC,kCAAkC;IAClC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,CAAA,GAAA,yHAAA,CAAA,kBAAe,AAAD;YAEd,OAAO,OAAO;+CAAG,CAAC,KAAK,KAAK,MAAM,KAAK;oBACrC,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;wBAC/B,OAAO,OAAO,IAAI,MAAM,OAAO;oBACjC;gBACF;;QACF;sCAAG,EAAE;IAEL,6EAA6E;IAC7E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,OAAO,CAAA,GAAA,mHAAA,CAAA,sBAAmB,AAAD;YAC/B,MAAM,KAAK,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD;YACxB,IAAI,CAAC,IAAI;YAET,oEAAoE;YACpE,GAAG,uBAAuB;+CAAC,CAAC;oBAC1B,MAAM,WAAgB,SAAS,IAAI,EAAE,YAAY,CAAC;oBAClD,SAAS,UAAU,GAAG;wBACpB,GAAI,SAAS,UAAU,IAAI,CAAC,CAAC;wBAC7B,UAAU;wBACV,OAAO,SAAS,MAAM,SAAS;oBACjC;oBACA,SAAS,IAAI,CAAE,QAAQ,GAAG;gBAC5B;;YAEA,iCAAiC;YACjC,GAAG,2BAA2B,CAAC;QACjC;sCAAG;QAAC;KAAQ;IAEZ,sBAAsB;IACtB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD,KAAK,cAAc;gBAAE,KAAK;YAAS;QAClD;sCAAG;QAAC;KAAS;IAEb,OAAO;AACT;GA1CM;;QACa,qIAAA,CAAA,cAAW;QACF,iJAAA,CAAA,aAAU;;;KAFhC;uCA4CS"}}, {"offset": {"line": 693, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 699, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/app/layout.tsx"], "sourcesContent": ["\"use client\";\r\nimport \"./globals.css\";\r\nimport { NotificationProvider } from \"@/hooks/useNotification\";\r\nimport NotificationBar from \"@/components/NotificationBar\";\r\nimport { SkillsProvider } from \"@/context/SkillsContext\";\r\nimport { Providers } from \"@/components/providers\";\r\nimport { EntitlementProvider } from \"@/context/EntitlementContext\";\r\nimport dynamic from \"next/dynamic\";\r\nimport React from \"react\";\r\nimport { usePathname } from \"next/navigation\";\r\nimport { TAB_ROUTE_MAP } from \"@/utils/tabRoutes\";\r\nimport AppInsightsClient from \"@/components/AppInsights\";\r\n\r\n// Move client-only logic to a separate component\r\n\r\nconst EntitlementReadyWrapper = dynamic(\r\n  () => import(\"@/components/HeaderTabs/EntitlementReadyWrapper\"),\r\n  {\r\n    ssr: false,\r\n  }\r\n);\r\n\r\n// Move client-only logic to a separate component\r\nfunction ClientLayout({ children }: { children: React.ReactNode }) {\r\n  const pathname = usePathname();\r\n  const routes = Object.values(TAB_ROUTE_MAP) as string[];\r\n  return (\r\n    <>\r\n      {routes.includes(pathname) && (\r\n        <>\r\n          <NotificationBar />\r\n          <EntitlementReadyWrapper />\r\n        </>\r\n      )}\r\n      <div className=\"h-full\">\r\n        <div className=\"mx-3\">{children}</div>\r\n      </div>\r\n    </>\r\n  );\r\n}\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <html lang=\"en\">\r\n      <body>\r\n        <Providers>\r\n          <AppInsightsClient />\r\n          <EntitlementProvider>\r\n            <SkillsProvider>\r\n              <NotificationProvider>\r\n                <ClientLayout>{children}</ClientLayout>\r\n              </NotificationProvider>\r\n            </SkillsProvider>\r\n          </EntitlementProvider>\r\n        </Providers>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;;;AAXA;;;;;;;;;;;AAaA,iDAAiD;AAEjD,MAAM,0BAA0B,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EACpC;;;;;;IAEE,KAAK;;KAHH;AAON,iDAAiD;AACjD,SAAS,aAAa,EAAE,QAAQ,EAAiC;;IAC/D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,OAAO,MAAM,CAAC,qHAAA,CAAA,gBAAa;IAC1C,qBACE;;YACG,OAAO,QAAQ,CAAC,2BACf;;kCACE,6LAAC,iIAAA,CAAA,UAAe;;;;;kCAChB,6LAAC;;;;;;;0BAGL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAAQ;;;;;;;;;;;;;AAI/B;GAhBS;;QACU,qIAAA,CAAA,cAAW;;;MADrB;AAkBM,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,6LAAC;QAAK,MAAK;kBACT,cAAA,6LAAC;sBACC,cAAA,6LAAC,2HAAA,CAAA,YAAS;;kCACR,6LAAC,4HAAA,CAAA,UAAiB;;;;;kCAClB,6LAAC,iIAAA,CAAA,sBAAmB;kCAClB,cAAA,6LAAC,4HAAA,CAAA,iBAAc;sCACb,cAAA,6LAAC,4HAAA,CAAA,uBAAoB;0CACnB,cAAA,6LAAC;8CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/B;MArBwB"}}, {"offset": {"line": 841, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}