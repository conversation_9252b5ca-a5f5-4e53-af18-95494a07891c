(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/components_HeaderTabs_HeaderTabsMenu_tsx_30ea92._.js", {

"[project]/components/HeaderTabs/HeaderTabsMenu.tsx [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
__turbopack_export_value__((__turbopack_import__) => {
    return Promise.all([
  "static/chunks/node_modules_next_1e3c68._.js",
  "static/chunks/node_modules_moment_5ca364._.js",
  "static/chunks/node_modules_moment-timezone_0f2c46._.js",
  "static/chunks/node_modules_4b3b05._.js",
  "static/chunks/_b8013b._.js",
  "static/chunks/components_HeaderTabs_HeaderTabsMenu_tsx_c03cdd._.js"
].map((chunk) => __turbopack_load__(chunk))).then(() => {
        return __turbopack_import__("[project]/components/HeaderTabs/HeaderTabsMenu.tsx [app-client] (ecmascript)");
    });
});
}}),
}]);