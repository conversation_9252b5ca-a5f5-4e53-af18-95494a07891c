{"version": 3, "sources": [], "sections": [{"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\";\r\nimport { twMerge } from \"tailwind-merge\";\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\nexport const USER_UUID_KEY = \"tg-user-uuid\";\r\n\r\nexport function getOrCreateUserUuid(): string {\r\n  if (typeof window === \"undefined\") return \"\";\r\n  let id = localStorage.getItem(USER_UUID_KEY);\r\n  if (!id) {\r\n    id = crypto.randomUUID();\r\n    localStorage.setItem(USER_UUID_KEY, id);\r\n  }\r\n  return id;\r\n}\r\n\r\nexport function clearUserUuid() {\r\n  localStorage.removeItem(USER_UUID_KEY); // call on logout if desired\r\n}\r\nexport enum APPLICATION_NAVIGATION_ROUTES {\r\n  VACANCY = \"Vacancy\",\r\n  WORK_FORCE_INDEX = \"Work_force_Index\",\r\n  SUB_CATEGORY = \"Sub_Catregory\",\r\n  SEARCH_MATCH = \"Search_Match\",\r\n  SC_SCORE_CONFIG = \"Sc_Score_Config\",\r\n}\r\nexport const emailInternalAddress = \"<EMAIL>\";\r\n\r\nexport enum FEATURE_NAMES {\r\n  SORTLIST_CANDIDATE_FEATURE = \"Shortlist Candidate Feature\",\r\n  THUMB_REVIEW_FEATURE = \"Thumb Review Feature\",\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,uIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,MAAM,gBAAgB;AAEtB,SAAS;IACd,wCAAmC,OAAO;;IAC1C,IAAI;AAMN;AAEO,SAAS;IACd,aAAa,UAAU,CAAC,gBAAgB,4BAA4B;AACtE;AACO,IAAA,AAAK,uDAAA;;;;;;WAAA;;AAOL,MAAM,uBAAuB;AAE7B,IAAA,AAAK,uCAAA;;;WAAA"}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/api/config.ts"], "sourcesContent": ["const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || \"http://0.0.0.0:8005\";\r\nconst PORTAL_SERVICE_BASE_URL =\r\n  process.env.DP_PORTAL_SERVICE || \"http://0.0.0.0:8006\";\r\n// Ensure this is the correct base URL for your experiment APIs,\r\n// the example uses localhost:8006, so adjust if necessary.\r\n// For this example, I will use the user-provided localhost:8006\r\nexport const EXPERIMENT_BASE_URL =\r\n  process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:8005\";\r\n\r\nexport const isADLogin = (): boolean =>\r\n  process.env.NEXT_PUBLIC_AD_LOGIN === \"true\";\r\n\r\nexport const isParsedResume =\r\n  process.env.NEXT_PUBLIC_PARSE_RESUME_FROM_MERCURY === \"true\";\r\n\r\nexport const IS_WHY_FIT_EDITABLE = process.env.NEXT_PUBLIC_IS_WHY_FIT_EDITABLE;\r\n\r\nexport const IS_LOCK_FEATURE_DISABLED =\r\n  process.env.NEXT_PUBLIC_IS_LOCK_FEATURE_DISABLED;\r\n\r\nexport const API_ENDPOINTS = {\r\n  categories: `${BASE_URL}/categories`,\r\n  subCategories: `${BASE_URL}/subcategories`,\r\n  subCategoriesPools: `${BASE_URL}/subcategory/pools`,\r\n  jobTitlesBySubCategory: `${BASE_URL}/jobtitles/:sub_category_id?limit=10000000`,\r\n  deleteAttribute: `${BASE_URL}/attribute/delete/:attribute_id`,\r\n  fetchAttributesBySubCategory: `${BASE_URL}/attributes/:sub_category_id?limit=10000000`,\r\n  fetchWeightsBySubCategory: `${BASE_URL}/weights/:sub_category_id`,\r\n  updateAttributeWeight: `${BASE_URL}/attributes/:sub_category_id/update`,\r\n  updateSubcategoryOfAttribute: `${BASE_URL}/attributes/:attribute_id/subcategory`,\r\n  updateAttributeApprovalStatus: `${BASE_URL}/attributes/:attribute_id/approval`,\r\n  candidatesData: `${BASE_URL}/candidates`,\r\n  updateCandidatesReviewData: `${BASE_URL}/candidates/update_in_db`,\r\n  jobsData: `${BASE_URL}/jobs`,\r\n  getVacancies: `${BASE_URL}/vacancies`,\r\n  getVacanciesFromFiles: `${BASE_URL}/files/vacancies`,\r\n  getCandidatesByVacancyId: `${BASE_URL}/candidates/:vacancy_id`,\r\n  getResumeByContactId: `${BASE_URL}/resume/:contact_id`,\r\n  getResumeFromFileByContactId: `${BASE_URL}/files/candidate-resume/:contact_id`,\r\n  getAllSubcategoryWeightConfigs: `${BASE_URL}/v1/subcategory/weight-configs`,\r\n  updateSubcategoryWeightConfig: `${BASE_URL}/v1/subcategory/weight-configs/:subcategory_id`,\r\n  getCandidateStats: `${BASE_URL}/api/candidate-stats`,\r\n  getEntitlements: `${PORTAL_SERVICE_BASE_URL}/api/entitlement`,\r\n  getVacancyConfigAndStatus: `${BASE_URL}/vacancies/:vacancy_id/status`,\r\n  startVacancyReview: `${BASE_URL}/vacancies/:vacancy_id/start`,\r\n  completeVacancyReview: `${BASE_URL}/vacancies/:vacancy_id/complete`,\r\n  updateWhyFitData: `${BASE_URL}/candidates/fitness_reason`,\r\n  vacanciesShortlisted: `${BASE_URL}/vacancies/shortlist`,\r\n  saveHistoryLogs: `${PORTAL_SERVICE_BASE_URL}/api/add_historical_data?email_id={email_id}&portal_name={portal_name}&feature={feature}`,\r\n  regenerateCatalystMatch: `${BASE_URL}/vacancies/:vacancy_id/regenerate-catalyst-match`,\r\n  catalystMatchStatus: `${BASE_URL}/vacancies/:vacancy_id/catalystmatchstatus`,\r\n\r\n  // New Experiment Endpoints\r\n  experimentGetVacancies: `${EXPERIMENT_BASE_URL}/experiment/vacancies`,\r\n  experimentGetVacancyRunDetails: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/details`,\r\n  experimentGetCandidatesForVacancyRun: `${EXPERIMENT_BASE_URL}/experiment/vacancies/:vacancy_id/runs/:run_id/candidates`,\r\n  experimentGetRunConfig: `${EXPERIMENT_BASE_URL}/experiment/runs/:run_id/config`,\r\n  experimentGetCandidateResume: `${EXPERIMENT_BASE_URL}/experiment/candidates/:contact_id/resume`,\r\n  experimentArchiveVacancy: `${EXPERIMENT_BASE_URL}/experiment/vacancies/archive`, // Added new endpoint\r\n  experimentPromoteResults: `${EXPERIMENT_BASE_URL}/experiment/results/promote`, // + New endpoint for promoting results\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAA,MAAM,WAAW,2DAAoC;AACrD,MAAM,0BACJ,QAAQ,GAAG,CAAC,iBAAiB,IAAI;AAI5B,MAAM,sBACX,2DAAoC;AAE/B,MAAM,YAAY,IACvB,6CAAqC;AAEhC,MAAM,iBACX,6CAAsD;AAEjD,MAAM;AAEN,MAAM;AAGN,MAAM,gBAAgB;IAC3B,YAAY,GAAG,SAAS,WAAW,CAAC;IACpC,eAAe,GAAG,SAAS,cAAc,CAAC;IAC1C,oBAAoB,GAAG,SAAS,kBAAkB,CAAC;IACnD,wBAAwB,GAAG,SAAS,0CAA0C,CAAC;IAC/E,iBAAiB,GAAG,SAAS,+BAA+B,CAAC;IAC7D,8BAA8B,GAAG,SAAS,2CAA2C,CAAC;IACtF,2BAA2B,GAAG,SAAS,yBAAyB,CAAC;IACjE,uBAAuB,GAAG,SAAS,mCAAmC,CAAC;IACvE,8BAA8B,GAAG,SAAS,qCAAqC,CAAC;IAChF,+BAA+B,GAAG,SAAS,kCAAkC,CAAC;IAC9E,gBAAgB,GAAG,SAAS,WAAW,CAAC;IACxC,4BAA4B,GAAG,SAAS,wBAAwB,CAAC;IACjE,UAAU,GAAG,SAAS,KAAK,CAAC;IAC5B,cAAc,GAAG,SAAS,UAAU,CAAC;IACrC,uBAAuB,GAAG,SAAS,gBAAgB,CAAC;IACpD,0BAA0B,GAAG,SAAS,uBAAuB,CAAC;IAC9D,sBAAsB,GAAG,SAAS,mBAAmB,CAAC;IACtD,8BAA8B,GAAG,SAAS,mCAAmC,CAAC;IAC9E,gCAAgC,GAAG,SAAS,8BAA8B,CAAC;IAC3E,+BAA+B,GAAG,SAAS,8CAA8C,CAAC;IAC1F,mBAAmB,GAAG,SAAS,oBAAoB,CAAC;IACpD,iBAAiB,GAAG,wBAAwB,gBAAgB,CAAC;IAC7D,2BAA2B,GAAG,SAAS,6BAA6B,CAAC;IACrE,oBAAoB,GAAG,SAAS,4BAA4B,CAAC;IAC7D,uBAAuB,GAAG,SAAS,+BAA+B,CAAC;IACnE,kBAAkB,GAAG,SAAS,0BAA0B,CAAC;IACzD,sBAAsB,GAAG,SAAS,oBAAoB,CAAC;IACvD,iBAAiB,GAAG,wBAAwB,wFAAwF,CAAC;IACrI,yBAAyB,GAAG,SAAS,gDAAgD,CAAC;IACtF,qBAAqB,GAAG,SAAS,0CAA0C,CAAC;IAE5E,2BAA2B;IAC3B,wBAAwB,GAAG,oBAAoB,qBAAqB,CAAC;IACrE,gCAAgC,GAAG,oBAAoB,sDAAsD,CAAC;IAC9G,sCAAsC,GAAG,oBAAoB,yDAAyD,CAAC;IACvH,wBAAwB,GAAG,oBAAoB,+BAA+B,CAAC;IAC/E,8BAA8B,GAAG,oBAAoB,yCAAyC,CAAC;IAC/F,0BAA0B,GAAG,oBAAoB,6BAA6B,CAAC;IAC/E,0BAA0B,GAAG,oBAAoB,2BAA2B,CAAC;AAC/E"}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 241, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/app/actions/getAppInsightsConnectionString.ts"], "sourcesContent": ["\"use server\";\r\n\r\nexport async function getAppInsightsConnectionString() {\r\n  return process.env.NEXT_PUBLIC_APPINSIGHTS_CONNECTION_STRING || \"\";\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAEO,eAAe,uCAA4B,GAA5B;IACpB,OAAO,mQAAyD;AAClE;;;IAFsB;;AAAA,iPAAA"}}, {"offset": {"line": 257, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/appInsights.ts"], "sourcesContent": ["import { getAppInsightsConnectionString } from \"@/app/actions/getAppInsightsConnectionString\";\r\nimport { ApplicationInsights } from \"@microsoft/applicationinsights-web\";\r\n\r\nlet appInsights: ApplicationInsights | null = null;\r\n\r\nexport async function initAppInsights() {\r\n  const appInsightsConnectionString = await getAppInsightsConnectionString();\r\n\r\n  if (!appInsights) {\r\n    const connectionString =\r\n      \"InstrumentationKey=\" + appInsightsConnectionString || \"\";\r\n    appInsights = new ApplicationInsights({\r\n      config: {\r\n        connectionString,\r\n        enableAutoRouteTracking: false, // We'll do this manually\r\n      },\r\n    });\r\n    appInsights.loadAppInsights();\r\n  }\r\n  return appInsights;\r\n}\r\n\r\nexport function getAppInsights() {\r\n  return appInsights;\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,IAAI,cAA0C;AAEvC,eAAe;IACpB,MAAM,8BAA8B,MAAM,CAAA,GAAA,kJAAA,CAAA,iCAA8B,AAAD;IAEvE,IAAI,CAAC,aAAa;QAChB,MAAM,mBACJ,wBAAwB,+BAA+B;QACzD,cAAc,IAAI,4OAAA,CAAA,sBAAmB,CAAC;YACpC,QAAQ;gBACN;gBACA,yBAAyB;YAC3B;QACF;QACA,YAAY,eAAe;IAC7B;IACA,OAAO;AACT;AAEO,SAAS;IACd,OAAO;AACT"}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/trackApi.ts"], "sourcesContent": ["// lib/trackedFetch.ts\r\nimport { getAppInsights } from \"./appInsights\";\r\nimport { getOrCreateUserUuid } from \"./utils\";\r\n\r\nexport async function trackedFetch(\r\n  input: RequestInfo | URL,\r\n  init: RequestInit = {},\r\n  extraCtx: Record<string, any> = {}\r\n): Promise<Response> {\r\n  const uuid = getOrCreateUserUuid();\r\n\r\n  // --- inject header so the backend can log/link this request too\r\n  const headers = new Headers(init.headers);\r\n  headers.set(\"X-User-UUID\", uuid);\r\n\r\n  const start = performance.now();\r\n  try {\r\n    const response = await fetch(input, { ...init, headers });\r\n    const dur = performance.now() - start;\r\n\r\n    getAppInsights()?.trackDependencyData({\r\n      id: crypto.randomUUID(), // per-call correlation id\r\n      name: typeof input === \"string\" ? input : input.toString(),\r\n      target: window.location.hostname,\r\n      duration: dur,\r\n      success: response.ok,\r\n      responseCode: response.status,\r\n      type: \"Fetch\",\r\n      properties: { userUuid: uuid, ...extraCtx },\r\n    });\r\n\r\n    if (!response.ok) throw new Error(`HTTP ${response.status}`);\r\n    return response;\r\n  } catch (err) {\r\n    const dur = performance.now() - start;\r\n    getAppInsights()?.trackDependencyData({\r\n      id: crypto.randomUUID(),\r\n      name: typeof input === \"string\" ? input : input.toString(),\r\n      target: window.location.hostname,\r\n      duration: dur,\r\n      success: false,\r\n      responseCode: 0,\r\n      type: \"Fetch\",\r\n      properties: {\r\n        userUuid: uuid,\r\n        error: err instanceof Error ? err.message : String(err),\r\n        ...extraCtx,\r\n      },\r\n    });\r\n    getAppInsights()?.trackException({\r\n      error: err as Error,\r\n      properties: { userUuid: uuid },\r\n    });\r\n    throw err;\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,sBAAsB;;;;AACtB;AACA;;;AAEO,eAAe,aACpB,KAAwB,EACxB,OAAoB,CAAC,CAAC,EACtB,WAAgC,CAAC,CAAC;IAElC,MAAM,OAAO,CAAA,GAAA,kHAAA,CAAA,sBAAmB,AAAD;IAE/B,iEAAiE;IACjE,MAAM,UAAU,IAAI,QAAQ,KAAK,OAAO;IACxC,QAAQ,GAAG,CAAC,eAAe;IAE3B,MAAM,QAAQ,YAAY,GAAG;IAC7B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,OAAO;YAAE,GAAG,IAAI;YAAE;QAAQ;QACvD,MAAM,MAAM,YAAY,GAAG,KAAK;QAEhC,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,KAAK,oBAAoB;YACpC,IAAI,OAAO,UAAU;YACrB,MAAM,OAAO,UAAU,WAAW,QAAQ,MAAM,QAAQ;YACxD,QAAQ,OAAO,QAAQ,CAAC,QAAQ;YAChC,UAAU;YACV,SAAS,SAAS,EAAE;YACpB,cAAc,SAAS,MAAM;YAC7B,MAAM;YACN,YAAY;gBAAE,UAAU;gBAAM,GAAG,QAAQ;YAAC;QAC5C;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE,MAAM,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;QAC3D,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,MAAM,MAAM,YAAY,GAAG,KAAK;QAChC,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,KAAK,oBAAoB;YACpC,IAAI,OAAO,UAAU;YACrB,MAAM,OAAO,UAAU,WAAW,QAAQ,MAAM,QAAQ;YACxD,QAAQ,OAAO,QAAQ,CAAC,QAAQ;YAChC,UAAU;YACV,SAAS;YACT,cAAc;YACd,MAAM;YACN,YAAY;gBACV,UAAU;gBACV,OAAO,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;gBACnD,GAAG,QAAQ;YACb;QACF;QACA,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,KAAK,eAAe;YAC/B,OAAO;YACP,YAAY;gBAAE,UAAU;YAAK;QAC/B;QACA,MAAM;IACR;AACF"}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/library/auth.ts"], "sourcesContent": ["import { API_ENDPOINTS } from \"@/api/config\";\r\nimport AzureADProvider from \"next-auth/providers/azure-ad\";\r\nimport { trackedFetch } from \"./trackApi\";\r\nimport { getAppInsights } from \"./appInsights\";\r\n\r\nexport const authOptions = {\r\n  providers: [\r\n    AzureADProvider({\r\n      clientId: process.env.RECRUITER_SSO_CLIENT_ID! as string,\r\n      clientSecret: process.env.RECRUITER_SSO_CLIENT_SECRET! as string,\r\n      tenantId: process.env.NEXT_PUBLIC_AZURE_TENANT_ID! as string,\r\n    }),\r\n  ],\r\n  secret: process.env.NEXTAUTH_SECRET!,\r\n  pages: {\r\n    signIn: `${process.env.NEXTAUTH_URL}/login`, // Custom sign-in page URL\r\n  },\r\n  callbacks: {\r\n    async redirect({ url, baseUrl }: { url: string; baseUrl: string }) {\r\n      if (url.startsWith(\"/\")) return `${baseUrl}${url}`;\r\n      if (new URL(url).origin === baseUrl) return url;\r\n      return baseUrl;\r\n    },\r\n\r\n    async session({ session }: any) {\r\n      if (\r\n        process.env.NEXT_PUBLIC_AD_LOGIN !== \"true\" &&\r\n        process.env.IS_ENTITLEMENT_ENABLED !== \"true\"\r\n      ) {\r\n        return session;\r\n      }\r\n      const portal_name = \"recruiter\";\r\n      let entitlement = {};\r\n      try {\r\n        // Caling an entitlements API to fetch user entitlements\r\n        const url = `${\r\n          API_ENDPOINTS.getEntitlements\r\n        }?email_id=${encodeURIComponent(\r\n          session?.user?.email\r\n        )}&portal_name=${encodeURIComponent(portal_name)}`;\r\n        const res = await trackedFetch(url, {}, { context: \"getEntitlements\" });\r\n\r\n        if (res.ok) {\r\n          const data = await res.json();\r\n          entitlement = data.entitlement;\r\n          getAppInsights()?.trackEvent({\r\n            name: \"FE_Entitlements_Fetched\",\r\n            properties: {\r\n              email: session?.user?.email,\r\n            },\r\n          });\r\n        } else {\r\n          console.error(\"Failed to fetch entitlements:\", res.statusText);\r\n        }\r\n      } catch (err) {\r\n        console.error(\"Error fetching entitlements:\", err);\r\n      }\r\n      return {\r\n        ...session,\r\n        entitlement,\r\n      };\r\n    },\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM,cAAc;IACzB,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAe,AAAD,EAAE;YACd,UAAU,QAAQ,GAAG,CAAC,uBAAuB;YAC7C,cAAc,QAAQ,GAAG,CAAC,2BAA2B;YACrD,QAAQ;QACV;KACD;IACD,QAAQ,QAAQ,GAAG,CAAC,eAAe;IACnC,OAAO;QACL,QAAQ,GAAG,QAAQ,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC;IAC7C;IACA,WAAW;QACT,MAAM,UAAS,EAAE,GAAG,EAAE,OAAO,EAAoC;YAC/D,IAAI,IAAI,UAAU,CAAC,MAAM,OAAO,GAAG,UAAU,KAAK;YAClD,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,SAAS,OAAO;YAC5C,OAAO;QACT;QAEA,MAAM,SAAQ,EAAE,OAAO,EAAO;YAC5B,uCAGE;;YAEF;YACA,MAAM,cAAc;YACpB,IAAI,cAAc,CAAC;YACnB,IAAI;gBACF,wDAAwD;gBACxD,MAAM,MAAM,GACV,+GAAA,CAAA,gBAAa,CAAC,eAAe,CAC9B,UAAU,EAAE,mBACX,SAAS,MAAM,OACf,aAAa,EAAE,mBAAmB,cAAc;gBAClD,MAAM,MAAM,MAAM,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,CAAC,GAAG;oBAAE,SAAS;gBAAkB;gBAErE,IAAI,IAAI,EAAE,EAAE;oBACV,MAAM,OAAO,MAAM,IAAI,IAAI;oBAC3B,cAAc,KAAK,WAAW;oBAC9B,CAAA,GAAA,wHAAA,CAAA,iBAAc,AAAD,KAAK,WAAW;wBAC3B,MAAM;wBACN,YAAY;4BACV,OAAO,SAAS,MAAM;wBACxB;oBACF;gBACF,OAAO;oBACL,QAAQ,KAAK,CAAC,iCAAiC,IAAI,UAAU;gBAC/D;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,gCAAgC;YAChD;YACA,OAAO;gBACL,GAAG,OAAO;gBACV;YACF;QACF;IACF;AACF"}}, {"offset": {"line": 424, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 430, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/utils/auth-utils.ts"], "sourcesContent": ["import { getServerSession } from \"next-auth\";\r\nimport { authOptions } from \"@/library/auth\";\r\n\r\nconst adAuthorization = process.env.NEXT_PUBLIC_AD_LOGIN === \"true\";\r\n\r\nexport async function CheckAuth(route?: string) {\r\n  const session = await getServerSession(authOptions);\r\n  const entitlement = session?.entitlement || {};\r\n  const isAuthorized = entitlement[route || \"\"] || undefined;\r\n\r\n  if (adAuthorization) {\r\n    if (session && isAuthorized) {\r\n      return true;\r\n    } else {\r\n      return false;\r\n    }\r\n  } else {\r\n    return true;\r\n  }\r\n}\r\n\r\nexport async function AuthErrorHandler() {\r\n  return new Response(\r\n    JSON.stringify({\r\n      message: \"Unauthenticated Access, please login to the portal\",\r\n    }),\r\n    {\r\n      status: 403,\r\n    }\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,kBAAkB,6CAAqC;AAEtD,eAAe,UAAU,KAAc;IAC5C,MAAM,UAAU,MAAM,CAAA,GAAA,uIAAA,CAAA,mBAAgB,AAAD,EAAE,iHAAA,CAAA,cAAW;IAClD,MAAM,cAAc,SAAS,eAAe,CAAC;IAC7C,MAAM,eAAe,WAAW,CAAC,SAAS,GAAG,IAAI;IAEjD,wCAAqB;QACnB,IAAI,WAAW,cAAc;YAC3B,OAAO;QACT,OAAO;YACL,OAAO;QACT;IACF,OAAO;;IAEP;AACF;AAEO,eAAe;IACpB,OAAO,IAAI,SACT,KAAK,SAAS,CAAC;QACb,SAAS;IACX,IACA;QACE,QAAQ;IACV;AAEJ"}}, {"offset": {"line": 460, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 466, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/app/api/pools/serverActions.ts"], "sourcesContent": ["import { API_ENDPOINTS } from \"@/api/config\";\r\n\r\nexport async function fetchSubCategoriesPools() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.subCategoriesPools);\r\n    const data = await response.json();\r\n    return data || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching subCategoriesPools:\", error);\r\n    return [];\r\n  }\r\n}\r\n\r\nexport async function fetchCandidateStats() {\r\n  try {\r\n    const response = await fetch(API_ENDPOINTS.getCandidateStats);\r\n    const data = await response.json();\r\n    return data || [];\r\n  } catch (error) {\r\n    console.error(\"Error fetching subCategoriesPools:\", error);\r\n    return [];\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,kBAAkB;QAC7D,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,QAAQ,EAAE;IACnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO,EAAE;IACX;AACF;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,+GAAA,CAAA,gBAAa,CAAC,iBAAiB;QAC5D,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,QAAQ,EAAE;IACnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO,EAAE;IACX;AACF"}}, {"offset": {"line": 492, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/git/push/tandym-dataprocessing/tandym-dp-web/tandym-dp-app/app/api/pools/route.ts"], "sourcesContent": ["import { APPLICATION_NAVIGATION_ROUTES } from \"@/library/utils\";\r\nimport { AuthError<PERSON><PERSON><PERSON>, CheckAuth } from \"@/utils/auth-utils\";\r\nimport { fetchSubCategoriesPools } from \"./serverActions\";\r\n\r\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\r\nlet cache: any = null;\r\nlet lastFetch = 0;\r\nconst TTL = 2 * 60 * 60 * 1000; // 2 hours in ms\r\n\r\nexport async function GET() {\r\n  const now = Date.now();\r\n  const response = await CheckAuth(\r\n    APPLICATION_NAVIGATION_ROUTES.WORK_FORCE_INDEX\r\n  );\r\n  if (response) {\r\n    if (cache && now - lastFetch < TTL) {\r\n      return Response.json(cache);\r\n    }\r\n    try {\r\n      const data = await fetchSubCategoriesPools();\r\n      cache = data;\r\n      lastFetch = now;\r\n      return Response.json(data);\r\n    } catch (error) {\r\n      console.error(\"Error fetching subCategoriesPools:\", error);\r\n      return new Response(\"Internal Server Error\", { status: 500 });\r\n    }\r\n  } else {\r\n    return AuthErrorHandler();\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,8DAA8D;AAC9D,IAAI,QAAa;AACjB,IAAI,YAAY;AAChB,MAAM,MAAM,IAAI,KAAK,KAAK,MAAM,gBAAgB;AAEzC,eAAe;IACpB,MAAM,MAAM,KAAK,GAAG;IACpB,MAAM,WAAW,MAAM,CAAA,GAAA,wHAAA,CAAA,YAAS,AAAD,EAC7B,kHAAA,CAAA,gCAA6B,CAAC,gBAAgB;IAEhD,IAAI,UAAU;QACZ,IAAI,SAAS,MAAM,YAAY,KAAK;YAClC,OAAO,SAAS,IAAI,CAAC;QACvB;QACA,IAAI;YACF,MAAM,OAAO,MAAM,CAAA,GAAA,sIAAA,CAAA,0BAAuB,AAAD;YACzC,QAAQ;YACR,YAAY;YACZ,OAAO,SAAS,IAAI,CAAC;QACvB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,IAAI,SAAS,yBAAyB;gBAAE,QAAQ;YAAI;QAC7D;IACF,OAAO;QACL,OAAO,CAAA,GAAA,wHAAA,CAAA,mBAAgB,AAAD;IACxB;AACF"}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}