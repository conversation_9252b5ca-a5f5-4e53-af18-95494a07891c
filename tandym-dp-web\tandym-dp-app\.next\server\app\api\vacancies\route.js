const CHUNK_PUBLIC_PATH = "server/app/api/vacancies/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_ae7053._.js");
runtime.loadChunk("server/chunks/[root of the server]__8a33cc._.js");
runtime.loadChunk("server/chunks/_820b56._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/vacancies/route/actions.js [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/app/api/vacancies/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
