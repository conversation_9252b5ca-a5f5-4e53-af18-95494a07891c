{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack://[project]/middleware.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\r\nimport { getToken } from \"next-auth/jwt\";\r\nimport type { NextRequest } from \"next/server\";\r\nimport { isADLogin } from \"@/api/config\";\r\n\r\nexport async function middleware(req: NextRequest) {\r\n  const url = req.nextUrl;\r\n  const pathname = url.pathname;\r\n  const isADlogin = isADLogin();\r\n\r\n  const isCandidateTuning = pathname.startsWith(\r\n    \"/CandidateTuning/For_Mercury_Portal\"\r\n  );\r\n  const referer = req.headers.get(\"referer\") || \"\";\r\n  const isFromCRM = referer.startsWith(process.env.CRM_URL || \"\");\r\n  // Skip AD auth if accessed from iframe or CRM referer\r\n if (isCandidateTuning && (isFromCRM)) {\r\n  return NextResponse.next();\r\n}\r\n\r\n  // Token fetched once\r\n  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });\r\n\r\n  const protectedRoutes = [\r\n    \"/\",\r\n    \"/candidates\",\r\n    \"/jobs\",\r\n    \"/skills-editor\",\r\n    \"/workforce-index\",\r\n    \"/vacancy\",\r\n    \"/experiments\",\r\n    \"/subcategory-config\",\r\n    // \"/subcategory-config\",\r\n    \"/CandidateTuning/For_Mercury_Portal\",\r\n  ];\r\n\r\n  const isProtected = protectedRoutes.some((path) => pathname.startsWith(path));\r\n\r\n  if (isADlogin && isProtected) {\r\n    // if (isCandidateTuning) {\r\n    //   const noAccessUrl = new URL(\"/no-access\", req.url);\r\n    //   return NextResponse.redirect(noAccessUrl);\r\n    // }\r\n    if (!token) {\r\n      // Avoid recursive callback loops by using the actual page URL\r\n      const signInUrl = new URL(\"/api/auth/signin/azure-ad\", req.url);\r\n      signInUrl.searchParams.set(\"callbackUrl\", req.url);\r\n\r\n      const response = NextResponse.redirect(signInUrl);\r\n\r\n      return response;\r\n    }\r\n  }\r\n\r\n  // Default fallback\r\n  return NextResponse.next();\r\n}\r\n\r\nexport const config = {\r\n  matcher: [\r\n    \"/\",\r\n    \"/candidates\",\r\n    \"/jobs\",\r\n    \"/skills-editor\",\r\n    \"/workforce-index\",\r\n    \"/vacancy\",\r\n    \"/vacancy/:path*\",\r\n    \"/experiments\",\r\n    // \"/subcategory-config\",\r\n    \"/CandidateTuning/For_Mercury_Portal\",\r\n  ], // Add other protected routes\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;;AADA;;;;AAKO,eAAe,WAAW,GAAgB;IAC/C,MAAM,MAAM,IAAI,OAAO;IACvB,MAAM,WAAW,IAAI,QAAQ;IAC7B,MAAM,YAAY;IAElB,MAAM,oBAAoB,SAAS,UAAU,CAC3C;IAEF,MAAM,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc;IAC9C,MAAM,YAAY,QAAQ,UAAU,CAAC,QAAQ,GAAG,CAAC,OAAO,IAAI;IAC5D,sDAAsD;IACvD,IAAI,qBAAsB,WAAY;QACrC,OAAO,qLAAA,CAAA,eAAY,CAAC,IAAI;IAC1B;IAEE,qBAAqB;IACrB,MAAM,QAAQ,MAAM,CAAA,GAAA,4IAAA,CAAA,WAAQ,AAAD,EAAE;QAAE;QAAK,QAAQ,QAAQ,GAAG,CAAC,eAAe;IAAC;IAExE,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yBAAyB;QACzB;KACD;IAED,MAAM,cAAc,gBAAgB,IAAI,CAAC,CAAC,OAAS,SAAS,UAAU,CAAC;IAEvE,IAAI,aAAa,aAAa;QAC5B,2BAA2B;QAC3B,wDAAwD;QACxD,+CAA+C;QAC/C,IAAI;QACJ,IAAI,CAAC,OAAO;YACV,8DAA8D;YAC9D,MAAM,YAAY,IAAI,IAAI,6BAA6B,IAAI,GAAG;YAC9D,UAAU,YAAY,CAAC,GAAG,CAAC,eAAe,IAAI,GAAG;YAEjD,MAAM,WAAW,qLAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;YAEvC,OAAO;QACT;IACF;IAEA,mBAAmB;IACnB,OAAO,qLAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,yBAAyB;QACzB;KACD;AACH"}}, {"offset": {"line": 97, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}