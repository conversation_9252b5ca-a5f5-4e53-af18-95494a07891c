location            = "eastus"
organization        = "tg"
environment         = "ua"
host_name           = "tg-uaue-env-apim001.azure-api.net"
client_host_name    = "candidate.ua.tandymgroup.com"
candidate_host_name = "client.ua.tandymgroup.com"

### App Config variables ###

AZURE_SQLSERVER_SERVER       = "az-azSqlDev-01.database.windows.net"
AZURE_SQLSERVER_DATABASE     = "dvSync.Sandbox"
AZURE_SHAREPOINT_SITE_URL    = "https://execusearchgroup.sharepoint.com/sites/MercuryUAT"
AZURE_DATAVERSE_RESOURCE_URL = "https://tandymgroup-sandbox.crm.dynamics.com"
AZURE_DATAVERSE_CRM_URL      = "https://tandymgroup-sandbox.crm.dynamics.com/api/data/v9.0/"
NEXTAUTH_URL                 = "https://recruiter.ua.tandymgroup.com"

### Shared DB connection ###

DBSharedConnectionUsername = "tandymdp"
DBSharedConnectionURL      = "tgdvueenvpsqlflex001.postgres.database.azure.com"
DBSharedConnectionDatabase = "tg-dvue-env-psqldb001"
DBSharedConnectionPort     = "5432"